# 📱 蛋糕店管理系统 APK 生成完整指南

## 🚨 当前环境问题

当前环境遇到以下问题：
1. **Java版本不兼容**：系统Java版本为1.8，但项目需要Java 11+
2. **网络连接问题**：Gradle下载超时
3. **环境配置问题**：JAVA_HOME指向无效目录

## 🔧 解决方案

### 方案1：修复本地环境（推荐）

#### 1. 安装Java 11或更高版本
```bash
# 下载并安装 OpenJDK 11
# 访问: https://adoptium.net/
# 或者安装 Oracle JDK 11+
```

#### 2. 设置环境变量
```bash
# Windows 设置
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-11.0.x-hotspot
set PATH=%JAVA_HOME%\bin;%PATH%

# 验证Java版本
java -version
javac -version
```

#### 3. 生成APK
```bash
# 清理项目
.\gradlew.bat clean

# 生成Debug APK
.\gradlew.bat assembleDebug

# 生成Release APK
.\gradlew.bat assembleRelease
```

### 方案2：使用Android Studio（最简单）

#### 1. 打开项目
- 启动Android Studio
- 打开项目文件夹 `c:\Users\<USER>\Desktop\cake`
- 等待Gradle同步完成

#### 2. 生成APK
- 菜单：`Build` → `Build Bundle(s) / APK(s)` → `Build APK(s)`
- 或者使用快捷键：`Ctrl + Shift + A` 搜索 "Build APK"

#### 3. 查找APK文件
- 构建完成后，点击通知中的 "locate" 链接
- 或者在 `app/build/outputs/apk/debug/` 目录查找

### 方案3：在线构建服务

#### 使用GitHub Actions（如果项目在GitHub上）
```yaml
# .github/workflows/build.yml
name: Build APK
on: [push]
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: actions/setup-java@v3
      with:
        java-version: '11'
        distribution: 'temurin'
    - name: Build APK
      run: ./gradlew assembleDebug
    - name: Upload APK
      uses: actions/upload-artifact@v3
      with:
        name: app-debug
        path: app/build/outputs/apk/debug/app-debug.apk
```

## 📦 APK文件位置

构建成功后，APK文件将位于：

### Debug版本（用于测试）
```
app/build/outputs/apk/debug/app-debug.apk
```

### Release版本（用于发布）
```
app/build/outputs/apk/release/app-release-unsigned.apk
```

## 🎯 测试账户信息

APK安装后的默认测试账户：

### 👨‍💼 管理员账户
```
用户名: 123123
密码:   123123
权限:   完整管理权限
```

### 👥 员工账户
```
用户名: employee
密码:   emp123
权限:   基础操作权限
```

## 📱 APK安装说明

### 1. 传输APK到Android设备
- 通过USB数据线复制
- 通过云存储（百度网盘、OneDrive等）
- 通过局域网传输工具

### 2. 安装APK
- 在Android设备上找到APK文件
- 点击安装（可能需要允许"未知来源"安装）
- 等待安装完成

### 3. 启动应用
- 在应用列表中找到"蛋糕店管理系统"
- 点击启动
- 使用测试账户登录

## 🔍 故障排除

### 构建失败
```bash
# 清理项目
.\gradlew.bat clean

# 检查Java版本
java -version

# 检查Gradle版本
.\gradlew.bat --version
```

### 网络问题
```bash
# 使用代理（如果需要）
.\gradlew.bat -Dhttp.proxyHost=proxy.company.com -Dhttp.proxyPort=8080 assembleDebug

# 或者配置gradle.properties
echo "systemProp.http.proxyHost=proxy.company.com" >> gradle.properties
echo "systemProp.http.proxyPort=8080" >> gradle.properties
```

### 签名问题（Release版本）
```bash
# 生成签名密钥
keytool -genkey -v -keystore my-release-key.keystore -alias alias_name -keyalg RSA -keysize 2048 -validity 10000

# 在app/build.gradle中配置签名
android {
    signingConfigs {
        release {
            storeFile file('my-release-key.keystore')
            storePassword 'password'
            keyAlias 'alias_name'
            keyPassword 'password'
        }
    }
    buildTypes {
        release {
            signingConfig signingConfigs.release
        }
    }
}
```

## 📊 APK信息

### 预期APK大小
- Debug版本：约 15-25 MB
- Release版本：约 10-20 MB

### 支持的Android版本
- 最低版本：Android 7.0 (API 24)
- 目标版本：Android 14 (API 35)

### 权限要求
- 网络访问（用于未来扩展）
- 存储访问（数据库文件）

## 🚀 快速命令

如果环境正常，使用以下命令快速生成APK：

```bash
# 一键生成Debug APK
.\gradlew.bat assembleDebug

# 一键生成Release APK
.\gradlew.bat assembleRelease

# 同时生成两个版本
.\gradlew.bat assemble
```

---

**注意**：如果遇到任何问题，建议使用Android Studio进行构建，这是最稳定可靠的方法。
