# 🎂 蛋糕店管理系统 - 部署运行指南

## 📋 系统要求

### 开发环境
- **Android Studio**: 2023.1.1 或更高版本
- **JDK**: 11 或更高版本
- **Android SDK**: API Level 24 (Android 7.0) 或更高
- **Gradle**: 8.0 或更高版本

### 运行环境
- **最低Android版本**: Android 7.0 (API Level 24)
- **推荐Android版本**: Android 10.0 (API Level 29) 或更高
- **内存要求**: 至少 2GB RAM
- **存储空间**: 至少 100MB 可用空间

## 🚀 快速开始

### 1. 项目导入
```bash
# 1. 克隆或下载项目到本地
# 2. 打开 Android Studio
# 3. 选择 "Open an existing Android Studio project"
# 4. 选择项目根目录（包含 build.gradle.kts 的目录）
# 5. 等待 Gradle 同步完成
```

### 2. 依赖配置
项目已配置所有必要的依赖，包括：
- **Room数据库**: 2.6.1
- **Jetpack Compose**: 最新版本
- **Material Design 3**: 最新版本
- **Lifecycle组件**: 2.7.0
- **Navigation组件**: 2.7.6

### 3. 编译运行
```bash
# 方法1: 使用Android Studio
# 1. 连接Android设备或启动模拟器
# 2. 点击 "Run" 按钮 (绿色三角形)
# 3. 选择目标设备
# 4. 等待应用安装和启动

# 方法2: 使用命令行
./gradlew assembleDebug
./gradlew installDebug
```

## 🔧 配置说明

### 数据库配置
- **数据库名称**: cakeshop_database
- **版本**: 7
- **存储位置**: 应用私有目录
- **迁移策略**: 自动迁移（fallbackToDestructiveMigration）

### 默认账户
系统启动时会自动创建以下测试账户：

#### 管理员账户
- **用户名**: 123123
- **密码**: 123123
- **权限**: 完整管理权限

#### 员工账户
- **用户名**: employee
- **密码**: emp123
- **权限**: 基础操作权限

## 📱 功能模块说明

### 1. 登录注册模块
- **登录界面**: LoginActivity
- **注册界面**: RegisterActivity
- **会话管理**: SessionManager

### 2. 主界面模块
- **主界面**: MainActivity
- **仪表板**: 实时统计数据显示
- **快速导航**: 各功能模块入口

### 3. 商品管理模块
- **商品列表**: ProductListActivity
- **添加商品**: AddProductActivity
- **商品分类**: 7种蛋糕类别

### 4. 客户管理模块
- **客户列表**: CustomerListActivity
- **添加客户**: AddCustomerActivity
- **客户等级**: 4级会员体系

### 5. 订单管理模块
- **订单列表**: OrderListActivity
- **新建订单**: AddOrderActivity
- **编辑订单**: EditOrderActivity
- **状态管理**: 6种订单状态

### 6. 购物系统模块
- **商品浏览**: ShoppingActivity
- **购物车**: CartActivity
- **订单结算**: CheckoutActivity

### 7. 钱包系统模块
- **钱包管理**: WalletActivity
- **充值功能**: 多种支付方式
- **交易记录**: 完整的交易历史

### 8. 用户管理模块
- **用户管理**: UserManagementActivity
- **权限控制**: 管理员专用
- **等级管理**: 客户等级升级

### 9. 员工管理模块
- **员工列表**: EmployeeListActivity
- **添加员工**: AddEmployeeActivity
- **职位管理**: 7种职位类型

### 10. 库存管理模块
- **库存列表**: InventoryListActivity
- **添加库存**: AddInventoryActivity
- **库存预警**: 低库存提醒

### 11. 报表统计模块
- **统计报表**: ReportsActivity
- **数据分析**: 销售和客户统计

## 🗄️ 数据库结构

### 核心业务表
1. **products** - 产品信息表
2. **customers_java** - 客户信息表
3. **orders** - 订单主表
4. **order_items** - 订单详情表
5. **employees_java** - 员工信息表
6. **inventory_java** - 库存信息表

### 系统功能表
7. **users_java** - 系统用户表
8. **cart_java** - 购物车表
9. **transactions_java** - 交易记录表

### 关键字段说明
- **客户表新增字段**:
  - `walletBalance`: 钱包余额
  - `customerLevel`: 客户等级
- **购物车表**:
  - 支持商品定制要求
  - 自动计算小计金额
- **交易表**:
  - 完整的交易流水记录
  - 支持多种交易类型

## 🔐 权限管理

### 管理员权限
- ✅ 所有功能模块访问
- ✅ 用户管理
- ✅ 员工管理
- ✅ 报表统计
- ✅ 系统配置

### 员工权限
- ✅ 产品管理
- ✅ 客户管理
- ✅ 订单管理
- ✅ 库存管理
- ❌ 用户管理
- ❌ 员工管理
- ❌ 报表统计

## 🐛 常见问题解决

### 1. 编译错误
```bash
# 清理项目
./gradlew clean

# 重新构建
./gradlew build
```

### 2. 数据库错误
- 卸载应用重新安装
- 或使用 `CakeShopDatabase.clearDatabase()` 清理数据库

### 3. 依赖问题
```bash
# 更新依赖
./gradlew --refresh-dependencies
```

### 4. 模拟器问题
- 使用 API Level 29 或更高的模拟器
- 确保模拟器有足够的内存和存储空间

## 📊 性能优化

### 数据库优化
- 使用索引加速查询
- 实现分页加载
- 使用 LiveData 响应式更新

### 内存优化
- 及时释放资源
- 使用 ViewHolder 模式
- 避免内存泄漏

### 界面优化
- 使用 RecyclerView 显示列表
- 实现懒加载
- 优化布局层次

## 🔄 版本更新

### 当前版本: 1.0
- ✅ 基础功能完整实现
- ✅ 用户注册登录
- ✅ 购物车和支付
- ✅ 管理员功能
- ✅ 客户等级系统

### 计划更新
- 📱 界面美化优化
- 🔔 消息推送功能
- 📈 更多统计报表
- 🌐 网络同步功能
- 🔒 数据加密安全

## 📞 技术支持

如果在部署或运行过程中遇到问题，请检查：
1. Android Studio 版本是否符合要求
2. 项目依赖是否正确下载
3. 设备或模拟器是否满足最低要求
4. 是否有足够的存储空间

## 🎉 部署完成

恭喜！您已成功部署蛋糕店管理系统。现在可以：
1. 使用默认账户登录测试
2. 创建新用户账户
3. 体验完整的业务流程
4. 管理商品、订单和客户
5. 使用购物车和支付功能

系统已准备好投入使用！
