@echo off
echo ========================================
echo 🎂 蛋糕店管理系统 - APK生成脚本
echo ========================================
echo.

echo 📋 检查环境...
if not exist "gradlew.bat" (
    echo ❌ 错误: 未找到 gradlew.bat，请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

echo ✅ Gradle Wrapper 已找到

echo 🔧 检查Java环境...
rem 尝试找到可用的Java
set "JAVA_CMD="
if exist "%JAVA_HOME%\bin\java.exe" (
    set "JAVA_CMD=%JAVA_HOME%\bin\java.exe"
    echo ✅ 使用JAVA_HOME中的Java: %JAVA_HOME%
) else (
    rem 尝试使用系统PATH中的Java
    java -version >nul 2>&1
    if %errorlevel% equ 0 (
        set "JAVA_CMD=java"
        echo ✅ 使用系统PATH中的Java
        rem 临时清除JAVA_HOME以避免冲突
        set "JAVA_HOME="
    ) else (
        echo ❌ 错误: 未找到可用的Java安装
        echo 请安装Java 11或更高版本，或正确设置JAVA_HOME环境变量
        pause
        exit /b 1
    )
)

echo.
echo 🧹 清理项目...
call gradlew.bat clean
if %errorlevel% neq 0 (
    echo ❌ 清理失败
    pause
    exit /b 1
)

echo.
echo 🏗️ 构建Debug APK...
call gradlew.bat assembleDebug
if %errorlevel% neq 0 (
    echo ❌ Debug APK构建失败
    pause
    exit /b 1
)

echo.
echo 🏗️ 构建Release APK...
call gradlew.bat assembleRelease
if %errorlevel% neq 0 (
    echo ⚠️ Release APK构建失败，但Debug APK已成功生成
    goto :show_debug_only
)

echo.
echo ✅ APK构建完成！
echo.
echo 📱 生成的APK文件位置：
echo.
echo 🔧 Debug版本（用于测试）：
echo    app\build\outputs\apk\debug\app-debug.apk
echo.
echo 🚀 Release版本（用于发布）：
echo    app\build\outputs\apk\release\app-release-unsigned.apk
echo.
echo 📊 APK信息：
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    for %%A in ("app\build\outputs\apk\debug\app-debug.apk") do (
        echo    Debug APK大小: %%~zA 字节
    )
)
if exist "app\build\outputs\apk\release\app-release-unsigned.apk" (
    for %%A in ("app\build\outputs\apk\release\app-release-unsigned.apk") do (
        echo    Release APK大小: %%~zA 字节
    )
)
echo.
echo 💡 使用说明：
echo    1. Debug版本可直接安装测试
echo    2. Release版本需要签名后才能安装
echo    3. 推荐使用Debug版本进行功能测试
echo.
echo 🎯 默认测试账户：
echo    管理员: 123123 / 123123
echo    员工:   employee / emp123
echo.
goto :end

:show_debug_only
echo.
echo ✅ Debug APK构建完成！
echo.
echo 📱 生成的APK文件位置：
echo.
echo 🔧 Debug版本（用于测试）：
echo    app\build\outputs\apk\debug\app-debug.apk
echo.
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    for %%A in ("app\build\outputs\apk\debug\app-debug.apk") do (
        echo    APK大小: %%~zA 字节
    )
)
echo.
echo 💡 使用说明：
echo    1. 可直接安装到Android设备进行测试
echo    2. 支持Android 7.0 (API 24) 及以上版本
echo    3. 首次启动会自动创建示例数据
echo.
echo 🎯 默认测试账户：
echo    管理员: 123123 / 123123
echo    员工:   employee / emp123
echo.

:end
echo 🎉 APK生成完成！您可以将APK文件传输到Android设备进行安装测试。
echo.
pause
