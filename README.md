# 🎂 甜蜜蛋糕店管理系统

一个基于Android Studio开发的完整蛋糕店管理系统，采用现代化的Material Design 3设计风格和MVC架构模式。

## 🆕 最新更新 - 完整功能实现

本系统已完整实现课程设计要求的所有功能：
- ✅ **用户注册登录系统** - 支持新用户注册和安全登录
- ✅ **客户购物下单功能** - 完整的购物车和下单流程
- ✅ **充值付款系统** - 钱包充值和多种支付方式
- ✅ **管理员商品管理** - 完整的商品CRUD操作
- ✅ **管理员订单管理** - 订单状态管理、发货、取消、删除
- ✅ **用户管理系统** - 添加删除用户、客户等级管理

## 🎯 核心功能亮点

### 🔐 用户系统
- **双重身份管理**：系统用户（管理员/员工）+ 客户档案
- **安全注册登录**：密码验证、用户名唯一性检查
- **权限分级控制**：管理员和员工权限明确区分
- **会话管理**：安全的登录状态保持

### 🛒 购物体验
- **商品浏览**：分类筛选、搜索功能
- **购物车管理**：添加、修改、删除商品
- **智能结算**：地址管理、支付方式选择
- **订单跟踪**：完整的订单状态流程

### 💰 支付系统
- **钱包功能**：余额充值、消费记录
- **多种支付**：现金、银行卡、支付宝、微信、钱包余额
- **交易记录**：完整的资金流水追踪
- **安全保障**：金额验证、余额检查

### 👑 会员体系
- **四级等级**：青铜、白银、黄金、铂金
- **自动升级**：基于消费金额的等级计算
- **专享折扣**：不同等级享受不同优惠
- **等级管理**：管理员可手动调整客户等级

### 📊 管理功能
- **订单管理**：状态更新、发货安排、订单取消
- **商品管理**：库存控制、价格调整、分类管理
- **用户管理**：添加员工、客户等级提升
- **数据统计**：实时销售数据、客户分析

## 📋 系统功能特色

### 🏗️ 技术架构
- **MVC架构模式**：清晰的模型-视图-控制器分层
- **Room数据库**：本地SQLite数据库，支持复杂查询和关联操作
- **Java + Android**：稳定可靠的开发技术栈
- **Material Design 3**：美观的用户界面设计
- **响应式设计**：适配不同屏幕尺寸

### 🗄️ 数据库设计（9个核心表）

#### 1. 产品表 (products)
- 产品基本信息：名称、描述、价格
- 分类管理：生日蛋糕、婚礼蛋糕、纸杯蛋糕等
- 规格选项：尺寸（6寸/8寸/10寸等）、口味（香草/巧克力/草莓等）
- 制作时间和可用状态管理

#### 2. 客户表 (customers)
- 客户基本信息：姓名、电话、邮箱、地址
- 客户分级：普通/VIP/高级/企业客户
- 消费统计：总订单数、总消费金额、积分

#### 3. 订单表 (orders)
- 订单基本信息：订单号、下单日期、交付日期
- 状态管理：待确认/已确认/制作中/待取货/已交付/已取消
- 支付管理：支付状态、支付方式、金额计算
- 特殊要求和配送信息

#### 4. 订单详情表 (order_items)
- 订单商品明细：产品、数量、单价、小计
- 定制要求记录
- 多表关联：订单-产品关联

#### 5. 员工表 (employees)
- 员工基本信息：工号、姓名、联系方式
- 职位管理：店长/烘焙师/裱花师/收银员等
- 部门划分：管理部/生产部/销售部/配送部
- 薪资和入职信息

#### 6. 库存表 (inventory)
- 物料管理：面粉、糖类、乳制品、装饰用品等
- 库存控制：当前库存、最低库存警戒线
- 供应商信息和存放位置
- 成本核算和过期日期管理

#### 7. 系统用户表 (users_java) 🆕
- 用户认证：用户名、密码、角色管理
- 权限控制：管理员和员工权限区分
- 登录管理：最后登录时间、账户状态
- 安全保障：密码加密、会话管理

#### 8. 购物车表 (cart_java) 🆕
- 商品暂存：客户购物车商品管理
- 数量控制：商品数量增减、小计计算
- 定制需求：特殊要求和备注信息
- 实时更新：购物车状态实时同步

#### 9. 交易记录表 (transactions_java) 🆕
- 资金流水：充值、支付、退款记录
- 余额管理：交易前后余额变化
- 支付方式：多种支付渠道记录
- 订单关联：交易与订单的关联关系

### 🎯 核心功能模块

#### 📊 首页仪表板
- **实时概览**：今日订单、收入、新客户统计
- **快速操作**：新建订单、添加客户、产品管理等
- **库存警告**：低库存物料提醒
- **最近活动**：系统操作日志展示

#### 🍰 产品管理模块
- **完整CRUD操作**：添加、查看、编辑、删除产品
- **智能搜索**：按名称、描述搜索产品
- **分类筛选**：按产品类别快速筛选
- **下拉选择**：产品类别、尺寸、口味的下拉选择
- **输入验证**：价格、制作时间等数值验证
- **可用性管理**：产品上下架状态控制

#### 👥 客户管理模块
- **客户档案**：完整的客户信息管理
- **分级管理**：不同客户类型的差异化服务
- **消费统计**：客户价值分析和积分管理
- **快速搜索**：按姓名、电话、邮箱搜索
- **关联查询**：查看客户历史订单

#### 📦 订单管理模块
- **订单全生命周期**：从下单到交付的完整流程
- **状态跟踪**：实时订单状态更新
- **支付管理**：多种支付方式支持
- **日期筛选**：按订单日期范围查询
- **金额计算**：自动计算折扣和最终金额

#### 👨‍💼 员工管理模块
- **员工档案**：完整的员工信息管理
- **职位管理**：不同职位的权限和职责
- **部门划分**：清晰的组织架构
- **薪资管理**：员工薪资信息记录

#### 📋 库存管理模块
- **物料分类**：按类别管理不同物料
- **库存预警**：低库存自动提醒
- **供应商管理**：供应商信息维护
- **成本控制**：物料成本核算
- **过期提醒**：临期物料预警

### 🎨 用户界面特色

#### Material Design 3风格
- **现代化设计**：符合最新设计规范
- **蛋糕店主题色彩**：温馨的粉色和棕色搭配
- **卡片式布局**：信息层次清晰
- **响应式设计**：适配不同屏幕尺寸

#### 交互体验优化
- **输入验证**：实时表单验证和错误提示
- **自动完成**：智能输入建议
- **下拉选择**：减少输入错误
- **搜索筛选**：快速定位目标信息
- **状态指示**：清晰的加载和错误状态

### 🔄 多表联动操作

#### 订单-客户-产品关联
- 创建订单时自动关联客户和产品信息
- 订单完成后自动更新客户消费统计
- 产品销量统计和库存扣减

#### 库存-生产联动
- 订单确认后预估原料消耗
- 生产完成后自动更新库存
- 采购建议基于销售预测

#### 员工-订单-生产流程
- 订单分配给相应技能的员工
- 生产进度跟踪和质量控制
- 员工工作量统计和绩效评估

## 🚀 技术实现亮点

### 数据库设计
- **外键约束**：确保数据完整性
- **索引优化**：提高查询性能
- **事务处理**：保证数据一致性

### 架构模式
- **Repository模式**：数据访问层抽象
- **ViewModel**：UI状态管理
- **Flow响应式编程**：实时数据更新

### 用户体验
- **离线支持**：本地数据库确保离线可用
- **数据验证**：多层次输入验证
- **错误处理**：友好的错误提示和恢复机制

## 📱 系统截图

*（这里可以添加应用的实际截图）*

## 🛠️ 开发环境

- **Android Studio**: 最新版本
- **Kotlin**: 1.9+
- **Compose BOM**: 最新稳定版
- **Room**: 2.6.1
- **Material 3**: 最新版本

## 🚀 快速开始

### 📦 安装和运行

1. 克隆项目到本地
2. 使用Android Studio打开项目
3. 等待Gradle同步完成
4. 连接Android设备或启动模拟器
5. 点击运行按钮

### 🔐 默认账户

系统启动后会自动创建测试账户：

**管理员账户**
- 用户名：`123123`
- 密码：`123123`
- 权限：完整管理权限

**员工账户**
- 用户名：`employee`
- 密码：`emp123`
- 权限：基础操作权限

### 🎯 功能演示流程

#### 1. 用户注册体验
```
登录界面 → 点击"新用户注册" → 填写注册信息 → 注册成功 → 返回登录
```

#### 2. 客户购物体验
```
主界面 → 客户购物 → 浏览商品 → 加入购物车 → 查看购物车 → 结算下单
```

#### 3. 钱包充值体验
```
主界面 → 我的钱包 → 选择充值金额 → 选择支付方式 → 确认充值 → 查看余额
```

#### 4. 管理员功能体验
```
管理员登录 → 订单管理 → 选择订单 → 管理操作 → 更改状态/发货/取消
```

#### 5. 用户管理体验
```
管理员登录 → 用户管理 → 添加用户 → 客户等级管理 → 升级客户等级
```

## 🎯 项目特色

✅ **功能完整**：涵盖蛋糕店运营的各个环节  
✅ **架构清晰**：标准MVC模式，代码结构清晰  
✅ **界面美观**：Material Design 3设计风格  
✅ **数据完整**：9个核心表，支持复杂业务逻辑
✅ **交互友好**：丰富的输入验证和用户反馈  
✅ **扩展性强**：模块化设计，易于功能扩展  

这个蛋糕店管理系统展示了现代Android应用开发的最佳实践，是学习和参考的优秀项目。
