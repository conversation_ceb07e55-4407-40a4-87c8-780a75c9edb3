# 📱 蛋糕店管理系统 - APK生成说明

## 🚀 方法一：使用Android Studio生成APK（推荐）

### 步骤1：打开项目
1. 启动 Android Studio
2. 选择 "Open an existing Android Studio project"
3. 选择项目根目录（包含 `build.gradle.kts` 的目录）
4. 等待 Gradle 同步完成

### 步骤2：生成APK
1. 在菜单栏选择 `Build` → `Build Bundle(s) / APK(s)` → `Build APK(s)`
2. 等待构建完成
3. 点击通知中的 "locate" 链接，或者手动找到APK文件

### APK文件位置
```
项目根目录/app/build/outputs/apk/debug/app-debug.apk
```

## 🔧 方法二：使用命令行生成APK

### Windows用户

#### 选项1：使用提供的脚本
```bash
# 双击运行
generate_apk.bat
```

#### 选项2：手动命令
```bash
# 在项目根目录打开命令提示符或PowerShell
cd "项目根目录"

# 清理项目
.\gradlew.bat clean

# 生成Debug APK
.\gradlew.bat assembleDebug
```

### Linux/Mac用户
```bash
# 在项目根目录打开终端
cd "项目根目录"

# 给予执行权限
chmod +x gradlew

# 清理项目
./gradlew clean

# 生成Debug APK
./gradlew assembleDebug
```

## 🐛 常见问题解决

### 问题1：JAVA_HOME错误
```
ERROR: JAVA_HOME is set to an invalid directory
```

**解决方案A：重新设置JAVA_HOME**
1. 找到Java安装目录（通常在 `C:\Program Files\Java\jdk-版本号`）
2. 设置环境变量：
   - Windows: 控制面板 → 系统 → 高级系统设置 → 环境变量
   - 新建系统变量：`JAVA_HOME` = Java安装路径

**解决方案B：使用Android Studio内置JDK**
1. 打开Android Studio
2. File → Settings → Build, Execution, Deployment → Build Tools → Gradle
3. 选择 "Use Gradle from: 'gradle-wrapper.properties' file"
4. Gradle JVM 选择 "Use Project JDK"

**解决方案C：临时清除JAVA_HOME**
```bash
# Windows PowerShell
$env:JAVA_HOME = ""
.\gradlew.bat assembleDebug

# Windows CMD
set JAVA_HOME=
gradlew.bat assembleDebug
```

### 问题2：Gradle同步失败
**解决方案：**
1. 检查网络连接
2. 在Android Studio中：File → Invalidate Caches and Restart
3. 删除 `.gradle` 文件夹后重新同步

### 问题3：构建失败
**解决方案：**
1. 确保Android SDK已正确安装
2. 检查 `local.properties` 文件中的SDK路径
3. 更新Android Studio和SDK到最新版本

## 📋 系统要求

### 开发环境要求
- **Android Studio**: 2023.1.1 或更高版本
- **JDK**: 11 或更高版本
- **Android SDK**: API Level 24 (Android 7.0) 或更高
- **Gradle**: 8.0 或更高版本

### 目标设备要求
- **最低Android版本**: Android 7.0 (API Level 24)
- **推荐Android版本**: Android 10.0 (API Level 29) 或更高
- **内存**: 至少 2GB RAM
- **存储空间**: 至少 100MB 可用空间

## 📦 APK类型说明

### Debug APK
- **文件名**: `app-debug.apk`
- **特点**: 
  - ✅ 可直接安装，无需签名
  - ✅ 包含调试信息
  - ✅ 适合开发和测试
  - ⚠️ 文件较大
- **用途**: 功能测试、演示

### Release APK
- **文件名**: `app-release-unsigned.apk`
- **特点**:
  - 🚀 经过优化，体积更小
  - 🚀 性能更好
  - ⚠️ 需要签名才能安装
  - ⚠️ 不包含调试信息
- **用途**: 正式发布

## 🎯 快速测试

### 生成并安装APK
```bash
# 1. 生成APK
.\gradlew.bat assembleDebug

# 2. 安装到连接的设备
adb install app\build\outputs\apk\debug\app-debug.apk

# 3. 启动应用
adb shell am start -n com.example.cakeshop/.MainActivity
```

### 默认测试账户
- **管理员**: 123123 / 123123
- **员工**: employee / emp123

## 📱 APK分享方式

### 方法1：直接传输
1. 将APK文件复制到U盘或SD卡
2. 在Android设备上安装文件管理器
3. 找到APK文件并点击安装

### 方法2：云存储分享
1. 上传APK到百度网盘、OneDrive等云存储
2. 在Android设备上下载APK
3. 点击安装

### 方法3：局域网传输
1. 使用文件传输工具（如茄子快传、SHAREit）
2. 通过WiFi直接传输APK文件

## 🔐 安全注意事项

### 安装前
1. 确保APK来源可信
2. 开启"未知来源"应用安装权限
3. 建议在测试设备上安装

### 安装后
1. 检查应用权限请求是否合理
2. 首次启动时会创建示例数据
3. 可以随时卸载应用

## 🎉 成功标志

APK生成成功后，您应该能看到：

1. **文件存在**: `app/build/outputs/apk/debug/app-debug.apk`
2. **文件大小**: 约15-25MB
3. **可以安装**: 在Android设备上正常安装
4. **可以运行**: 应用启动无错误
5. **功能正常**: 登录、注册、购物等功能正常

## 📞 获取帮助

如果遇到问题：

1. **检查错误日志**: 查看构建过程中的错误信息
2. **重新构建**: 尝试清理后重新构建
3. **更新工具**: 确保Android Studio和SDK是最新版本
4. **查看文档**: 参考Android官方文档

祝您构建成功！🎂✨
