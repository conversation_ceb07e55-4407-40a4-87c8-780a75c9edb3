# 📱 蛋糕店管理系统 - APK安装指南

## 🚀 快速生成APK

### Windows用户
```bash
# 双击运行或在命令行执行
generate_apk.bat
```

### Linux/Mac用户
```bash
# 在终端执行
chmod +x generate_apk.sh
./generate_apk.sh
```

### 手动构建
```bash
# 清理项目
./gradlew clean

# 生成Debug APK（推荐用于测试）
./gradlew assembleDebug

# 生成Release APK（需要签名）
./gradlew assembleRelease
```

## 📁 APK文件位置

构建完成后，APK文件将生成在以下位置：

### Debug版本（推荐）
```
app/build/outputs/apk/debug/app-debug.apk
```
- ✅ 可直接安装
- ✅ 包含调试信息
- ✅ 适合功能测试

### Release版本
```
app/build/outputs/apk/release/app-release-unsigned.apk
```
- ⚠️ 需要签名才能安装
- 🚀 体积更小，性能更好
- 📦 适合正式发布

## 📲 安装方法

### 方法1：ADB安装（推荐）
```bash
# 确保设备已连接并开启USB调试
adb devices

# 安装APK
adb install app/build/outputs/apk/debug/app-debug.apk

# 如果已安装，强制覆盖安装
adb install -r app/build/outputs/apk/debug/app-debug.apk
```

### 方法2：文件传输安装
1. 将APK文件传输到Android设备
2. 在设备上找到APK文件
3. 点击安装（需要允许未知来源应用）

### 方法3：无线安装
1. 将APK上传到云存储或文件共享服务
2. 在Android设备上下载APK
3. 点击安装

## ⚙️ 设备要求

### 最低系统要求
- **Android版本**: Android 7.0 (API Level 24) 或更高
- **内存**: 至少 2GB RAM
- **存储空间**: 至少 100MB 可用空间
- **架构**: ARM64, ARM, x86, x86_64

### 推荐配置
- **Android版本**: Android 10.0 (API Level 29) 或更高
- **内存**: 4GB RAM 或更多
- **存储空间**: 500MB 可用空间

## 🔧 安装前准备

### 1. 开启未知来源应用
**Android 8.0及以上**：
1. 设置 → 安全 → 未知来源
2. 找到用于安装的应用（如文件管理器）
3. 开启"允许来自此来源"

**Android 7.x及以下**：
1. 设置 → 安全 → 未知来源
2. 开启"未知来源"选项

### 2. 开启USB调试（ADB安装）
1. 设置 → 关于手机
2. 连续点击"版本号"7次开启开发者选项
3. 设置 → 开发者选项 → USB调试
4. 开启"USB调试"

## 🎯 首次启动

### 默认账户
应用首次启动时会自动创建以下测试账户：

**管理员账户**
- 用户名：`admin`
- 密码：`admin123`
- 权限：完整管理权限

**员工账户**
- 用户名：`employee`
- 密码：`emp123`
- 权限：基础操作权限

### 示例数据
系统会自动创建以下示例数据：
- 🍰 示例产品（经典生日蛋糕、巧克力慕斯蛋糕等）
- 👥 示例客户数据
- 📦 示例订单数据
- 📋 示例库存数据

## 🔍 功能验证

### 基础功能测试
1. **登录测试**：使用默认账户登录
2. **注册测试**：创建新用户账户
3. **购物测试**：浏览商品，添加到购物车
4. **支付测试**：钱包充值和支付功能
5. **管理测试**：订单管理、用户管理等

### 权限测试
1. **管理员权限**：访问用户管理、报表统计
2. **员工权限**：基础操作功能
3. **权限限制**：验证权限控制是否正常

## 🐛 常见问题

### 安装失败
**问题**：APK安装失败
**解决方案**：
1. 检查设备存储空间是否充足
2. 确认已开启"未知来源"应用安装
3. 尝试卸载旧版本后重新安装
4. 检查APK文件是否完整

### 应用闪退
**问题**：应用启动后立即闪退
**解决方案**：
1. 检查设备Android版本是否满足要求
2. 重启设备后重试
3. 清除应用数据后重新启动
4. 检查设备内存是否充足

### 数据库错误
**问题**：数据库相关错误
**解决方案**：
1. 清除应用数据
2. 卸载后重新安装
3. 确保设备有足够存储空间

### 网络权限
**问题**：某些功能需要网络权限
**解决方案**：
1. 检查应用权限设置
2. 确保网络连接正常
3. 允许应用访问网络

## 📊 APK信息

### 文件大小
- **Debug APK**: 约 15-25 MB
- **Release APK**: 约 10-20 MB

### 权限列表
应用请求的权限：
- `INTERNET` - 网络访问（未来功能）
- `WRITE_EXTERNAL_STORAGE` - 存储访问
- `READ_EXTERNAL_STORAGE` - 读取存储

### 支持架构
- ARM64-v8a (主流64位设备)
- ARMv7 (32位设备)
- x86 (模拟器)
- x86_64 (64位模拟器)

## 🎉 安装完成

安装成功后，您可以：

1. **🔐 登录系统**：使用默认账户或注册新账户
2. **🛒 体验购物**：浏览商品、添加购物车、下单支付
3. **💰 钱包功能**：充值、查看交易记录
4. **👑 会员系统**：体验客户等级和折扣
5. **🔧 管理功能**：订单管理、用户管理（管理员）

## 📞 技术支持

如果在安装或使用过程中遇到问题：

1. **检查系统要求**：确保设备满足最低要求
2. **查看错误日志**：通过ADB查看详细错误信息
3. **重新构建**：尝试重新生成APK
4. **清理安装**：卸载后重新安装

祝您使用愉快！🎂✨
