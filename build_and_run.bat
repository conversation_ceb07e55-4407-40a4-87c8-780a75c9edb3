@echo off
echo ========================================
echo 🎂 蛋糕店管理系统 - 构建和运行脚本
echo ========================================
echo.

echo 📋 检查环境...
where adb >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到 ADB，请确保 Android SDK 已正确安装
    pause
    exit /b 1
)

echo ✅ ADB 已找到

echo.
echo 🔧 清理项目...
call gradlew clean
if %errorlevel% neq 0 (
    echo ❌ 清理失败
    pause
    exit /b 1
)

echo.
echo 🏗️ 构建项目...
call gradlew assembleDebug
if %errorlevel% neq 0 (
    echo ❌ 构建失败
    pause
    exit /b 1
)

echo.
echo 📱 检查设备连接...
adb devices
echo.

echo 📦 安装应用...
call gradlew installDebug
if %errorlevel% neq 0 (
    echo ❌ 安装失败，请检查设备连接
    pause
    exit /b 1
)

echo.
echo ✅ 构建和安装完成！
echo.
echo 🎯 默认测试账户：
echo    管理员: 123123 / 123123
echo    员工:   employee / emp123
echo.
echo 🚀 应用已安装到设备，请在设备上启动"蛋糕店管理系统"
echo.
pause
