# 🎂 蛋糕店管理系统 - 项目结构

## 📁 完整项目结构

```
蛋糕店管理系统/
├── app/
│   ├── build.gradle.kts                    # 应用级构建配置
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/com/example/cakeshop/
│   │   │   │   ├── model/                  # 📊 数据模型层
│   │   │   │   │   ├── Product.kt          # 产品模型 + 枚举
│   │   │   │   │   ├── Customer.kt         # 客户模型 + 枚举
│   │   │   │   │   ├── Order.kt            # 订单模型 + 枚举
│   │   │   │   │   ├── Employee.kt         # 员工模型 + 枚举
│   │   │   │   │   └── Inventory.kt        # 库存模型 + 枚举
│   │   │   │   │
│   │   │   │   ├── database/               # 🗄️ 数据访问层
│   │   │   │   │   ├── ProductDao.kt       # 产品数据访问
│   │   │   │   │   ├── CustomerDao.kt      # 客户数据访问
│   │   │   │   │   ├── OrderDao.kt         # 订单数据访问
│   │   │   │   │   ├── EmployeeDao.kt      # 员工数据访问
│   │   │   │   │   ├── InventoryDao.kt     # 库存数据访问
│   │   │   │   │   ├── CakeShopDatabase.kt # 数据库主类
│   │   │   │   │   └── Converters.kt       # 类型转换器
│   │   │   │   │
│   │   │   │   ├── repository/             # 🏪 仓储层
│   │   │   │   │   └── CakeShopRepository.kt # 数据仓储统一接口
│   │   │   │   │
│   │   │   │   ├── viewmodel/              # 🎮 视图模型层
│   │   │   │   │   └── ProductViewModel.kt # 产品视图模型
│   │   │   │   │
│   │   │   │   ├── view/                   # 🎨 视图层
│   │   │   │   │   ├── CakeShopApp.kt      # 主应用组件
│   │   │   │   │   └── screens/            # 功能屏幕
│   │   │   │   │       ├── HomeScreen.kt           # 首页
│   │   │   │   │       ├── ProductListScreen.kt    # 产品列表
│   │   │   │   │       ├── AddEditProductDialog.kt # 产品编辑
│   │   │   │   │       ├── CustomerListScreen.kt   # 客户列表
│   │   │   │   │       ├── OrderListScreen.kt      # 订单列表
│   │   │   │   │       ├── EmployeeListScreen.kt   # 员工列表
│   │   │   │   │       └── InventoryListScreen.kt  # 库存列表
│   │   │   │   │
│   │   │   │   ├── utils/                  # 🛠️ 工具类
│   │   │   │   │   └── SampleDataInitializer.kt # 示例数据初始化
│   │   │   │   │
│   │   │   │   ├── ui/theme/               # 🎨 主题配置
│   │   │   │   │   ├── Color.kt            # 颜色定义
│   │   │   │   │   ├── Theme.kt            # 主题配置
│   │   │   │   │   └── Type.kt             # 字体配置
│   │   │   │   │
│   │   │   │   └── MainActivity.kt         # 主活动
│   │   │   │
│   │   │   ├── res/                        # 📱 资源文件
│   │   │   │   ├── values/
│   │   │   │   │   ├── colors.xml          # 颜色资源
│   │   │   │   │   ├── strings.xml         # 字符串资源
│   │   │   │   │   └── themes.xml          # 主题资源
│   │   │   │   ├── drawable/               # 图标资源
│   │   │   │   ├── mipmap-*/               # 应用图标
│   │   │   │   └── xml/                    # XML配置
│   │   │   │
│   │   │   └── AndroidManifest.xml         # 应用清单
│   │   │
│   │   └── test/
│   │       └── java/com/example/cakeshop/
│   │           └── DatabaseTest.kt         # 🧪 数据库测试
│   │
│   └── proguard-rules.pro                  # 代码混淆规则
│
├── gradle/                                 # Gradle配置
├── build.gradle.kts                        # 项目级构建配置
├── settings.gradle.kts                     # 项目设置
├── README.md                               # 📖 项目说明
├── 项目总结.md                             # 📋 项目完成总结
├── 使用说明.md                             # 📱 用户使用指南
└── 项目结构.md                             # 📁 本文件
```

## 🏗️ 架构层次说明

### 📊 Model层 (数据模型)
**位置**: `app/src/main/java/com/example/cakeshop/model/`

**职责**:
- 定义数据实体类
- 包含Room数据库注解
- 定义业务相关的枚举类
- 数据验证和约束

**文件说明**:
- `Product.kt` - 产品实体 + 产品类别/尺寸/口味枚举
- `Customer.kt` - 客户实体 + 客户类型枚举
- `Order.kt` - 订单实体 + 订单状态/支付状态/支付方式枚举
- `Employee.kt` - 员工实体 + 职位/部门枚举
- `Inventory.kt` - 库存实体 + 物料类别/单位枚举

### 🗄️ Database层 (数据访问)
**位置**: `app/src/main/java/com/example/cakeshop/database/`

**职责**:
- 定义数据访问对象(DAO)
- 数据库配置和管理
- SQL查询定义
- 数据类型转换

**文件说明**:
- `*Dao.kt` - 各实体的数据访问接口
- `CakeShopDatabase.kt` - Room数据库主类
- `Converters.kt` - 数据类型转换器

### 🏪 Repository层 (数据仓储)
**位置**: `app/src/main/java/com/example/cakeshop/repository/`

**职责**:
- 统一数据访问接口
- 业务逻辑封装
- 数据源抽象
- 缓存策略

### 🎮 ViewModel层 (视图模型)
**位置**: `app/src/main/java/com/example/cakeshop/viewmodel/`

**职责**:
- UI状态管理
- 业务逻辑处理
- 数据绑定
- 生命周期感知

### 🎨 View层 (用户界面)
**位置**: `app/src/main/java/com/example/cakeshop/view/`

**职责**:
- UI组件定义
- 用户交互处理
- 界面导航
- 数据展示

**screens/目录说明**:
- `HomeScreen.kt` - 首页仪表板
- `ProductListScreen.kt` - 产品管理界面
- `AddEditProductDialog.kt` - 产品编辑对话框
- `CustomerListScreen.kt` - 客户管理界面
- `OrderListScreen.kt` - 订单管理界面
- `EmployeeListScreen.kt` - 员工管理界面
- `InventoryListScreen.kt` - 库存管理界面

## 🔄 数据流向

```
用户操作 → View层 → ViewModel层 → Repository层 → Database层 → SQLite数据库
    ↑                                                                    ↓
    ←←←←←←←←←←←←←←← UI更新 ←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←
```

## 📋 核心功能模块

### 🍰 产品管理
- **CRUD操作**: 完整的增删改查功能
- **分类管理**: 7种产品类别
- **规格选择**: 5种尺寸 + 9种口味
- **搜索筛选**: 实时搜索和分类筛选

### 👥 客户管理
- **客户档案**: 完整的客户信息管理
- **分级服务**: 4种客户类型
- **消费统计**: 订单数、消费额、积分
- **快速搜索**: 多字段搜索功能

### 📦 订单管理
- **状态跟踪**: 7种订单状态
- **支付管理**: 4种支付状态 + 5种支付方式
- **关联查询**: 客户-订单-产品关联
- **日期管理**: 下单日期和交付日期

### 👨‍💼 员工管理
- **职位管理**: 7种职位类型
- **部门划分**: 5个部门
- **薪资管理**: 员工薪资记录
- **状态控制**: 在职/离职状态

### 📋 库存管理
- **分类管理**: 12种物料类别
- **库存预警**: 低库存自动提醒
- **供应商管理**: 供应商信息维护
- **成本核算**: 单位成本和总价值

## 🎯 技术特色

### 现代化技术栈
- **Jetpack Compose**: 声明式UI框架
- **Room数据库**: 类型安全的SQLite封装
- **Kotlin协程**: 异步编程和Flow响应式数据流
- **Material Design 3**: 最新设计规范

### 架构优势
- **MVC模式**: 清晰的分层架构
- **单一职责**: 每个类职责明确
- **依赖注入**: 松耦合设计
- **响应式编程**: 实时数据更新

### 用户体验
- **输入验证**: 多层次验证机制
- **错误处理**: 友好的错误提示
- **加载状态**: 清晰的状态指示
- **离线支持**: 本地数据库保证可用性

这个项目结构展示了现代Android应用开发的最佳实践，是学习和参考的优秀范例。
