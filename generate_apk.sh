#!/bin/bash

echo "========================================"
echo "🎂 蛋糕店管理系统 - APK生成脚本"
echo "========================================"
echo

echo "📋 检查环境..."
if [ ! -f "gradlew" ]; then
    echo "❌ 错误: 未找到 gradlew，请确保在项目根目录运行此脚本"
    exit 1
fi

echo "✅ Gradle Wrapper 已找到"

# 确保gradlew有执行权限
chmod +x gradlew

echo
echo "🧹 清理项目..."
./gradlew clean
if [ $? -ne 0 ]; then
    echo "❌ 清理失败"
    exit 1
fi

echo
echo "🏗️ 构建Debug APK..."
./gradlew assembleDebug
if [ $? -ne 0 ]; then
    echo "❌ Debug APK构建失败"
    exit 1
fi

echo
echo "🏗️ 构建Release APK..."
./gradlew assembleRelease
if [ $? -ne 0 ]; then
    echo "⚠️ Release APK构建失败，但Debug APK已成功生成"
    show_debug_only=true
else
    show_debug_only=false
fi

echo
if [ "$show_debug_only" = true ]; then
    echo "✅ Debug APK构建完成！"
    echo
    echo "📱 生成的APK文件位置："
    echo
    echo "🔧 Debug版本（用于测试）："
    echo "   app/build/outputs/apk/debug/app-debug.apk"
    echo
    if [ -f "app/build/outputs/apk/debug/app-debug.apk" ]; then
        size=$(stat -f%z "app/build/outputs/apk/debug/app-debug.apk" 2>/dev/null || stat -c%s "app/build/outputs/apk/debug/app-debug.apk" 2>/dev/null)
        echo "   APK大小: $size 字节"
    fi
else
    echo "✅ APK构建完成！"
    echo
    echo "📱 生成的APK文件位置："
    echo
    echo "🔧 Debug版本（用于测试）："
    echo "   app/build/outputs/apk/debug/app-debug.apk"
    echo
    echo "🚀 Release版本（用于发布）："
    echo "   app/build/outputs/apk/release/app-release-unsigned.apk"
    echo
    echo "📊 APK信息："
    if [ -f "app/build/outputs/apk/debug/app-debug.apk" ]; then
        size=$(stat -f%z "app/build/outputs/apk/debug/app-debug.apk" 2>/dev/null || stat -c%s "app/build/outputs/apk/debug/app-debug.apk" 2>/dev/null)
        echo "   Debug APK大小: $size 字节"
    fi
    if [ -f "app/build/outputs/apk/release/app-release-unsigned.apk" ]; then
        size=$(stat -f%z "app/build/outputs/apk/release/app-release-unsigned.apk" 2>/dev/null || stat -c%s "app/build/outputs/apk/release/app-release-unsigned.apk" 2>/dev/null)
        echo "   Release APK大小: $size 字节"
    fi
fi

echo
echo "💡 使用说明："
echo "   1. Debug版本可直接安装测试"
if [ "$show_debug_only" = false ]; then
    echo "   2. Release版本需要签名后才能安装"
    echo "   3. 推荐使用Debug版本进行功能测试"
else
    echo "   2. 支持Android 7.0 (API 24) 及以上版本"
    echo "   3. 首次启动会自动创建示例数据"
fi
echo
echo "🎯 默认测试账户："
echo "   管理员: admin / admin123"
echo "   员工:   employee / emp123"
echo
echo "🎉 APK生成完成！您可以将APK文件传输到Android设备进行安装测试。"
echo
