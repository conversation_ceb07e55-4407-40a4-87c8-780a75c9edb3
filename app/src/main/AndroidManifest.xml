<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Cakeshop"
        tools:targetApi="31">
        <activity
            android:name=".LoginActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.Cakeshop"
            android:screenOrientation="unspecified"
            android:configChanges="orientation|screenSize|keyboardHidden">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".MainActivity"
            android:label="主界面"
            android:theme="@style/Theme.Cakeshop"
            android:screenOrientation="unspecified"
            android:configChanges="orientation|screenSize|keyboardHidden" />

        <activity
            android:name=".ProductListActivity"
            android:label="产品管理"
            android:theme="@style/Theme.Cakeshop"
            android:screenOrientation="unspecified"
            android:configChanges="orientation|screenSize|keyboardHidden" />

        <activity
            android:name=".CustomerListActivity"
            android:label="客户管理"
            android:theme="@style/Theme.Cakeshop"
            android:screenOrientation="unspecified"
            android:configChanges="orientation|screenSize|keyboardHidden" />

        <activity
            android:name=".OrderListActivity"
            android:exported="true"
            android:label="订单管理"
            android:theme="@style/Theme.MaterialComponents.Light.NoActionBar"
            android:screenOrientation="unspecified"
            android:configChanges="orientation|screenSize|keyboardHidden">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>

        <activity
            android:name=".EmployeeListActivity"
            android:label="员工管理"
            android:theme="@style/Theme.Cakeshop"
            android:screenOrientation="unspecified"
            android:configChanges="orientation|screenSize|keyboardHidden" />

        <activity
            android:name=".InventoryListActivity"
            android:label="库存管理"
            android:theme="@style/Theme.Cakeshop"
            android:screenOrientation="unspecified"
            android:configChanges="orientation|screenSize|keyboardHidden" />

        <activity
            android:name=".AddProductActivity"
            android:label="添加产品"
            android:theme="@style/Theme.Cakeshop"
            android:screenOrientation="unspecified"
            android:configChanges="orientation|screenSize|keyboardHidden" />

        <activity
            android:name=".AddCustomerActivity"
            android:label="添加客户"
            android:theme="@style/Theme.Cakeshop"
            android:screenOrientation="unspecified"
            android:configChanges="orientation|screenSize|keyboardHidden" />

        <activity
            android:name=".AddOrderActivity"
            android:label="新建订单"
            android:theme="@style/Theme.Cakeshop"
            android:screenOrientation="unspecified"
            android:configChanges="orientation|screenSize|keyboardHidden" />

        <activity
            android:name=".ReportsActivity"
            android:label="报表统计"
            android:theme="@style/Theme.Cakeshop"
            android:screenOrientation="unspecified"
            android:configChanges="orientation|screenSize|keyboardHidden" />

        <activity
            android:name=".AddEmployeeActivity"
            android:label="添加员工"
            android:theme="@style/Theme.Cakeshop"
            android:screenOrientation="unspecified"
            android:configChanges="orientation|screenSize|keyboardHidden" />

        <activity
            android:name=".AddInventoryActivity"
            android:label="添加库存"
            android:theme="@style/Theme.Cakeshop"
            android:screenOrientation="unspecified"
            android:configChanges="orientation|screenSize|keyboardHidden" />

        <activity
            android:name=".SimpleMainActivity"
            android:label="简化版本"
            android:theme="@style/Theme.Cakeshop"
            android:screenOrientation="unspecified"
            android:configChanges="orientation|screenSize|keyboardHidden" />

        <activity
            android:name=".EditOrderActivity"
            android:label="编辑订单"
            android:theme="@style/Theme.Cakeshop"
            android:screenOrientation="unspecified"
            android:configChanges="orientation|screenSize|keyboardHidden" />

        <activity
            android:name=".RegisterActivity"
            android:label="用户注册"
            android:theme="@style/Theme.Cakeshop"
            android:screenOrientation="unspecified"
            android:configChanges="orientation|screenSize|keyboardHidden" />

        <activity
            android:name=".ShoppingActivity"
            android:label="客户购物"
            android:theme="@style/Theme.Cakeshop"
            android:screenOrientation="unspecified"
            android:configChanges="orientation|screenSize|keyboardHidden" />

        <activity
            android:name=".CartActivity"
            android:label="购物车"
            android:theme="@style/Theme.Cakeshop"
            android:screenOrientation="unspecified"
            android:configChanges="orientation|screenSize|keyboardHidden" />

        <activity
            android:name=".CheckoutActivity"
            android:label="订单结算"
            android:theme="@style/Theme.Cakeshop"
            android:screenOrientation="unspecified"
            android:configChanges="orientation|screenSize|keyboardHidden" />

        <activity
            android:name=".WalletActivity"
            android:label="我的钱包"
            android:theme="@style/Theme.Cakeshop"
            android:screenOrientation="unspecified"
            android:configChanges="orientation|screenSize|keyboardHidden" />

        <activity
            android:name=".UserManagementActivity"
            android:label="用户管理"
            android:theme="@style/Theme.Cakeshop"
            android:screenOrientation="unspecified"
            android:configChanges="orientation|screenSize|keyboardHidden" />
    </application>

</manifest>