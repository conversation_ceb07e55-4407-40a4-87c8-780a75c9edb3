<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Welcome Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            
            
            >

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:id="@+id/welcome_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="欢迎使用甜蜜蛋糕店管理系统"
                    android:textColor="@android:color/white"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/current_date"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="2024年05月28日 星期二"
                    android:textColor="@android:color/white"
                    android:textSize="14sp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Quick Actions Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:text="快速操作"
            android:textColor="@color/text_primary"
            android:textSize="18sp"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:orientation="horizontal">

            <androidx.cardview.widget.CardView
                android:id="@+id/new_order_card"
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                
                
                android:clickable="true"
                android:focusable="true"
                android:foreground="?android:attr/selectableItemBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="12dp">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@drawable/ic_add"
                         />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="新建订单"
                        android:textColor="@color/text_primary"
                        android:textSize="12sp"
                        android:textStyle="bold" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/add_customer_card"
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                
                
                android:clickable="true"
                android:focusable="true"
                android:foreground="?android:attr/selectableItemBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="12dp">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@drawable/ic_person_add"
                         />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="添加客户"
                        android:textColor="@color/text_primary"
                        android:textSize="12sp"
                        android:textStyle="bold" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:orientation="horizontal">

            <androidx.cardview.widget.CardView
                android:id="@+id/manage_products_card"
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                
                
                android:clickable="true"
                android:focusable="true"
                android:foreground="?android:attr/selectableItemBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="12dp">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@drawable/ic_cake"
                         />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="产品管理"
                        android:textColor="@color/text_primary"
                        android:textSize="12sp"
                        android:textStyle="bold" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/check_inventory_card"
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                
                
                android:clickable="true"
                android:focusable="true"
                android:foreground="?android:attr/selectableItemBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="12dp">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@drawable/ic_inventory"
                         />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="库存查看"
                        android:textColor="@color/text_primary"
                        android:textSize="12sp"
                        android:textStyle="bold" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

        <!-- Statistics Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:text="今日统计"
            android:textColor="@color/text_primary"
            android:textSize="18sp"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:orientation="horizontal">

            <!-- Today Orders -->
            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                
                >

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="今日订单"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/today_order_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="12"
                        android:textColor="@color/primary_color"
                        android:textSize="24sp"
                        android:textStyle="bold" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Today Revenue -->
            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                
                >

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="今日收入"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/today_revenue"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="¥2,580"
                        android:textColor="@color/secondary_color"
                        android:textSize="20sp"
                        android:textStyle="bold" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <!-- New Customers -->
            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                
                >

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="新客户"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/new_customer_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="5"
                        android:textColor="@color/accent_color"
                        android:textSize="24sp"
                        android:textStyle="bold" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Low Stock -->
            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                
                >

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="库存警告"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/low_stock_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="3"
                        android:textColor="@color/warning_color"
                        android:textSize="24sp"
                        android:textStyle="bold" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </LinearLayout>

</ScrollView>
