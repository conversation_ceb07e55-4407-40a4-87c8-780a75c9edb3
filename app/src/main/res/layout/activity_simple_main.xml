<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFF8E1">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp"
        android:gravity="center_horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="🎂"
            android:textSize="80sp"
            android:layout_marginBottom="24dp" />

        <TextView
            android:id="@+id/welcome_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="欢迎使用甜蜜蛋糕店管理系统"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="#E91E63"
            android:layout_marginBottom="16dp"
            android:gravity="center"
            android:textAlignment="center" />

        <TextView
            android:id="@+id/version_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Java版本"
            android:textSize="18sp"
            android:textColor="#FF9800"
            android:layout_marginBottom="32dp"
            android:gravity="center"
            android:textAlignment="center" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="#FFFFFF"
            android:padding="20dp"
            android:layout_marginBottom="24dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="系统功能"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="#212121"
                android:layout_marginBottom="12dp" />

            <TextView
                android:id="@+id/features_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="功能列表"
                android:textSize="16sp"
                android:textColor="#757575"
                android:lineSpacingExtra="4dp" />

        </LinearLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="技术栈：Java + Android SDK + Room数据库"
            android:textSize="14sp"
            android:textColor="#9E9E9E"
            android:layout_marginTop="16dp"
            android:gravity="center"
            android:textAlignment="center" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="编译成功 ✓"
            android:textSize="16sp"
            android:textColor="#4CAF50"
            android:textStyle="bold"
            android:layout_marginTop="24dp"
            android:gravity="center"
            android:textAlignment="center" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="如果您看到这个界面，说明APK运行正常！"
            android:textSize="14sp"
            android:textColor="#2196F3"
            android:layout_marginTop="32dp"
            android:gravity="center"
            android:textAlignment="center"
            android:background="#E3F2FD"
            android:padding="12dp" />

    </LinearLayout>

</ScrollView>
