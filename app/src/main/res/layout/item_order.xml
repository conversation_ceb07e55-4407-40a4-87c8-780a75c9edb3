<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="16dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/orderIdText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/black" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/editButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="编辑"
                android:textSize="12sp"
                app:cornerRadius="4dp"
                style="@style/Widget.MaterialComponents.Button" />
        </LinearLayout>

        <TextView
            android:id="@+id/orderNumberText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textColor="@color/gray"
            android:layout_marginBottom="8dp" />

        <TextView
            android:id="@+id/priceAndDateText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="@color/primary_color"
            android:layout_marginBottom="4dp" />

        <TextView
            android:id="@+id/paymentStatusText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="@color/gray" />

    </LinearLayout>
</com.google.android.material.card.MaterialCardView>