<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone"/>

        <!-- 头部欢迎区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@color/primary_color"
            android:padding="24dp"
            android:layout_marginBottom="20dp"
            android:elevation="4dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="🎂"
                android:textSize="32sp"
                android:gravity="center"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/welcome_message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="欢迎使用甜蜜蛋糕店管理系统"
                android:textColor="@android:color/white"
                android:textSize="20sp"
                android:textStyle="bold"
                android:gravity="center"
                android:layout_marginBottom="4dp" />

            <TextView
                android:id="@+id/current_date"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="2024年05月29日 星期三"
                android:textColor="@android:color/white"
                android:textSize="14sp"
                android:gravity="center"
                android:alpha="0.9" />

        </LinearLayout>

        <!-- 快速操作按钮 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="快速操作"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="#212121"
            android:layout_marginBottom="12dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <Button
                android:id="@+id/btn_products"
                android:layout_width="0dp"
                android:layout_height="90dp"
                android:layout_weight="1"
                android:layout_marginEnd="6dp"
                android:text="🍰\n产品管理"
                android:textSize="14sp"
                android:textStyle="bold"
                android:background="@color/secondary_color"
                android:textColor="@color/text_primary"
                android:elevation="2dp" />

            <Button
                android:id="@+id/btn_customers"
                android:layout_width="0dp"
                android:layout_height="90dp"
                android:layout_weight="1"
                android:layout_marginStart="3dp"
                android:layout_marginEnd="3dp"
                android:text="👥\n客户管理"
                android:textSize="14sp"
                android:textStyle="bold"
                android:background="@color/success_color"
                android:textColor="@android:color/white"
                android:elevation="2dp" />

            <Button
                android:id="@+id/btn_orders"
                android:layout_width="0dp"
                android:layout_height="90dp"
                android:layout_weight="1"
                android:layout_marginStart="6dp"
                android:text="📋\n订单管理"
                android:textSize="14sp"
                android:textStyle="bold"
                android:background="@color/info_color"
                android:textColor="@android:color/white"
                android:elevation="2dp" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="24dp">

            <Button
                android:id="@+id/btn_employees"
                android:layout_width="0dp"
                android:layout_height="90dp"
                android:layout_weight="1"
                android:layout_marginEnd="6dp"
                android:text="👨‍💼\n员工管理"
                android:textSize="14sp"
                android:textStyle="bold"
                android:background="@color/accent_color"
                android:textColor="@android:color/white"
                android:elevation="2dp" />

            <Button
                android:id="@+id/btn_inventory"
                android:layout_width="0dp"
                android:layout_height="90dp"
                android:layout_weight="1"
                android:layout_marginStart="3dp"
                android:layout_marginEnd="3dp"
                android:text="📦\n库存管理"
                android:textSize="14sp"
                android:textStyle="bold"
                android:background="@color/warning_color"
                android:textColor="@android:color/white"
                android:elevation="2dp" />

            <Button
                android:id="@+id/btn_reports"
                android:layout_width="0dp"
                android:layout_height="90dp"
                android:layout_weight="1"
                android:layout_marginStart="6dp"
                android:text="📊\n报表统计"
                android:textSize="14sp"
                android:textStyle="bold"
                android:background="@color/primary_dark"
                android:textColor="@android:color/white"
                android:elevation="2dp" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="24dp"/>

        <!-- 今日统计 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="今日统计"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="#212121"
            android:layout_marginBottom="12dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <!-- 今日订单 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="110dp"
                android:layout_weight="1"
                android:orientation="vertical"
                android:background="@color/card_background"
                android:padding="16dp"
                android:layout_marginEnd="6dp"
                android:gravity="center"
                android:elevation="3dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📋 今日订单"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/today_orders_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="12"
                    android:textSize="28sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary_color"
                    android:layout_marginTop="8dp" />

            </LinearLayout>

            <!-- 今日收入 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="110dp"
                android:layout_weight="1"
                android:orientation="vertical"
                android:background="@color/card_background"
                android:padding="16dp"
                android:layout_marginStart="6dp"
                android:gravity="center"
                android:elevation="3dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="💰 今日收入"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/today_revenue_amount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="¥2,580"
                    android:textSize="22sp"
                    android:textStyle="bold"
                    android:textColor="@color/success_color"
                    android:layout_marginTop="8dp" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <!-- 总客户数 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_weight="1"
                android:orientation="vertical"
                android:background="@android:color/white"
                android:padding="16dp"
                android:layout_marginEnd="4dp"
                android:gravity="center">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="总客户"
                    android:textSize="14sp"
                    android:textColor="#757575" />

                <TextView
                    android:id="@+id/total_customers_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="45"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:textColor="#4CAF50"
                    android:layout_marginTop="8dp" />

            </LinearLayout>

            <!-- 库存警告 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_weight="1"
                android:orientation="vertical"
                android:background="@android:color/white"
                android:padding="16dp"
                android:layout_marginStart="4dp"
                android:gravity="center">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="库存警告"
                    android:textSize="14sp"
                    android:textColor="#757575" />

                <TextView
                    android:id="@+id/low_stock_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="3"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:textColor="#F44336"
                    android:layout_marginTop="8dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- 版本信息 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="甜蜜蛋糕店管理系统 v1.0 (Java版本)\n技术栈：Java + Android SDK + Room数据库"
            android:textSize="12sp"
            android:textColor="#9E9E9E"
            android:layout_marginTop="24dp"
            android:gravity="center"
            android:background="#F5F5F5"
            android:padding="12dp" />

    </LinearLayout>

</ScrollView>