<resources>
    <string name="app_name">甜蜜蛋糕店</string>

    <!-- 导航标题 -->
    <string name="nav_home">首页</string>
    <string name="nav_products">产品管理</string>
    <string name="nav_customers">客户管理</string>
    <string name="nav_orders">订单管理</string>
    <string name="nav_employees">员工管理</string>
    <string name="nav_inventory">库存管理</string>

    <!-- 通用操作 -->
    <string name="action_add">添加</string>
    <string name="action_edit">编辑</string>
    <string name="action_delete">删除</string>
    <string name="action_save">保存</string>
    <string name="action_cancel">取消</string>
    <string name="action_search">搜索</string>

    <!-- 产品相关 -->
    <string name="product_name">产品名称</string>
    <string name="product_description">产品描述</string>
    <string name="product_price">价格</string>
    <string name="product_category">产品类别</string>
    <string name="product_size">蛋糕尺寸</string>
    <string name="product_flavor">蛋糕口味</string>
    <string name="product_preparation_time">制作时间</string>
    <string name="product_available">产品可用</string>

    <!-- 客户相关 -->
    <string name="customer_name">客户姓名</string>
    <string name="customer_phone">联系电话</string>
    <string name="customer_email">电子邮箱</string>
    <string name="customer_address">联系地址</string>
    <string name="customer_type">客户类型</string>

    <!-- 订单相关 -->
    <string name="order_number">订单编号</string>
    <string name="order_date">下单日期</string>
    <string name="order_delivery_date">交付日期</string>
    <string name="order_status">订单状态</string>
    <string name="order_amount">订单金额</string>

    <!-- 员工相关 -->
    <string name="employee_number">员工编号</string>
    <string name="employee_name">员工姓名</string>
    <string name="employee_position">职位</string>
    <string name="employee_department">部门</string>
    <string name="employee_salary">薪资</string>

    <!-- 库存相关 -->
    <string name="inventory_item_name">物料名称</string>
    <string name="inventory_category">物料类别</string>
    <string name="inventory_current_stock">当前库存</string>
    <string name="inventory_min_stock">最低库存</string>
    <string name="inventory_supplier">供应商</string>
</resources>