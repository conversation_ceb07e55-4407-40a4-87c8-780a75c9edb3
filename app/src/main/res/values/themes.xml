<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Theme.Cakeshop" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryDark">@color/primary_dark</item>
        <item name="colorAccent">@color/accent_color</item>
        <item name="colorSecondary">@color/accent_color</item>
        <item name="colorSecondaryVariant">@color/accent_color</item>
        <item name="android:windowBackground">@color/background_color</item>
        <item name="android:statusBarColor">?attr/colorPrimaryDark</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
    </style>
</resources>