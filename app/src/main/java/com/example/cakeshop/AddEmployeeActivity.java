package com.example.cakeshop;

import android.app.Activity;
import android.os.Bundle;
import android.view.View;
import android.widget.*;
import com.example.cakeshop.model.Employee;
import com.example.cakeshop.repository.CakeShopRepository;

public class AddEmployeeActivity extends Activity {
    
    private EditText empIdEdit, nameEdit, phoneEdit, salaryEdit, emailEdit, hireDateEdit, notesEdit;
    private Spinner positionSpinner, departmentSpinner;
    private CheckBox activeCheckBox;
    private CakeShopRepository repository;
    private boolean isEditMode = false;
    private long employeeId = -1;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        repository = new CakeShopRepository(getApplication());
        isEditMode = getIntent().getBooleanExtra("EDIT_MODE", false);
        
        createLayout();
        setupSpinners();
        
        if (isEditMode) {
            loadEmployeeData();
        }
    }
    
    private void createLayout() {
        ScrollView scrollView = new ScrollView(this);
        scrollView.setBackgroundColor(0xFFFCE4EC);
        
        LinearLayout mainLayout = new LinearLayout(this);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.setPadding(32, 32, 32, 32);
        
        // 标题
        TextView titleText = new TextView(this);
        titleText.setText(isEditMode ? "👨‍💼 编辑员工" : "👨‍💼 添加新员工");
        titleText.setTextSize(24);
        titleText.setTextColor(0xFFFF6F00);
        titleText.setPadding(0, 0, 0, 32);
        mainLayout.addView(titleText);
        
        // 表单容器
        LinearLayout formLayout = new LinearLayout(this);
        formLayout.setOrientation(LinearLayout.VERTICAL);
        formLayout.setBackgroundColor(0xFFFFFFFF);
        formLayout.setPadding(24, 24, 24, 24);
        
        LinearLayout.LayoutParams editParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 120);
        editParams.setMargins(0, 8, 0, 16);
        
        // 员工工号
        TextView empIdLabel = new TextView(this);
        empIdLabel.setText("员工工号 *");
        empIdLabel.setTextSize(16);
        empIdLabel.setTextColor(0xFF3E2723);
        empIdLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(empIdLabel);
        
        empIdEdit = new EditText(this);
        empIdEdit.setHint("请输入员工工号");
        empIdEdit.setPadding(16, 16, 16, 16);
        empIdEdit.setBackgroundColor(0xFFF5F5F5);
        empIdEdit.setLayoutParams(editParams);
        formLayout.addView(empIdEdit);
        
        // 员工姓名
        TextView nameLabel = new TextView(this);
        nameLabel.setText("员工姓名 *");
        nameLabel.setTextSize(16);
        nameLabel.setTextColor(0xFF3E2723);
        nameLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(nameLabel);
        
        nameEdit = new EditText(this);
        nameEdit.setHint("请输入员工姓名");
        nameEdit.setPadding(16, 16, 16, 16);
        nameEdit.setBackgroundColor(0xFFF5F5F5);
        nameEdit.setLayoutParams(editParams);
        formLayout.addView(nameEdit);
        
        // 职位
        TextView positionLabel = new TextView(this);
        positionLabel.setText("职位 *");
        positionLabel.setTextSize(16);
        positionLabel.setTextColor(0xFF3E2723);
        positionLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(positionLabel);
        
        positionSpinner = new Spinner(this);
        positionSpinner.setPadding(16, 16, 16, 16);
        positionSpinner.setBackgroundColor(0xFFF5F5F5);
        positionSpinner.setLayoutParams(editParams);
        formLayout.addView(positionSpinner);
        
        // 部门
        TextView departmentLabel = new TextView(this);
        departmentLabel.setText("部门 *");
        departmentLabel.setTextSize(16);
        departmentLabel.setTextColor(0xFF3E2723);
        departmentLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(departmentLabel);
        
        departmentSpinner = new Spinner(this);
        departmentSpinner.setPadding(16, 16, 16, 16);
        departmentSpinner.setBackgroundColor(0xFFF5F5F5);
        departmentSpinner.setLayoutParams(editParams);
        formLayout.addView(departmentSpinner);
        
        // 联系电话
        TextView phoneLabel = new TextView(this);
        phoneLabel.setText("联系电话");
        phoneLabel.setTextSize(16);
        phoneLabel.setTextColor(0xFF3E2723);
        phoneLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(phoneLabel);
        
        phoneEdit = new EditText(this);
        phoneEdit.setHint("请输入联系电话");
        phoneEdit.setInputType(android.text.InputType.TYPE_CLASS_PHONE);
        phoneEdit.setPadding(16, 16, 16, 16);
        phoneEdit.setBackgroundColor(0xFFF5F5F5);
        phoneEdit.setLayoutParams(editParams);
        formLayout.addView(phoneEdit);

        // 电子邮箱
        TextView emailLabel = new TextView(this);
        emailLabel.setText("电子邮箱");
        emailLabel.setTextSize(16);
        emailLabel.setTextColor(0xFF3E2723);
        emailLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(emailLabel);

        emailEdit = new EditText(this);
        emailEdit.setHint("请输入电子邮箱");
        emailEdit.setInputType(android.text.InputType.TYPE_TEXT_VARIATION_EMAIL_ADDRESS);
        emailEdit.setPadding(16, 16, 16, 16);
        emailEdit.setBackgroundColor(0xFFF5F5F5);
        emailEdit.setLayoutParams(editParams);
        formLayout.addView(emailEdit);

        // 薪资
        TextView salaryLabel = new TextView(this);
        salaryLabel.setText("薪资 (元/月)");
        salaryLabel.setTextSize(16);
        salaryLabel.setTextColor(0xFF3E2723);
        salaryLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(salaryLabel);

        salaryEdit = new EditText(this);
        salaryEdit.setHint("请输入月薪");
        salaryEdit.setInputType(android.text.InputType.TYPE_CLASS_NUMBER | android.text.InputType.TYPE_NUMBER_FLAG_DECIMAL);
        salaryEdit.setPadding(16, 16, 16, 16);
        salaryEdit.setBackgroundColor(0xFFF5F5F5);
        salaryEdit.setLayoutParams(editParams);
        formLayout.addView(salaryEdit);

        // 入职日期
        TextView hireDateLabel = new TextView(this);
        hireDateLabel.setText("入职日期");
        hireDateLabel.setTextSize(16);
        hireDateLabel.setTextColor(0xFF3E2723);
        hireDateLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(hireDateLabel);

        hireDateEdit = new EditText(this);
        hireDateEdit.setHint("请输入入职日期 (YYYY-MM-DD)");
        hireDateEdit.setPadding(16, 16, 16, 16);
        hireDateEdit.setBackgroundColor(0xFFF5F5F5);
        hireDateEdit.setLayoutParams(editParams);
        formLayout.addView(hireDateEdit);

        // 备注
        TextView notesLabel = new TextView(this);
        notesLabel.setText("备注");
        notesLabel.setTextSize(16);
        notesLabel.setTextColor(0xFF3E2723);
        notesLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(notesLabel);

        notesEdit = new EditText(this);
        notesEdit.setHint("请输入备注信息");
        notesEdit.setInputType(android.text.InputType.TYPE_TEXT_FLAG_MULTI_LINE);
        notesEdit.setLines(3);
        notesEdit.setPadding(16, 16, 16, 16);
        notesEdit.setBackgroundColor(0xFFF5F5F5);
        notesEdit.setLayoutParams(editParams);
        formLayout.addView(notesEdit);

        // 在职状态
        activeCheckBox = new CheckBox(this);
        activeCheckBox.setText("在职状态");
        activeCheckBox.setTextSize(16);
        activeCheckBox.setTextColor(0xFF3E2723);
        activeCheckBox.setChecked(true);
        activeCheckBox.setPadding(0, 16, 0, 16);
        formLayout.addView(activeCheckBox);
        
        // 按钮容器
        LinearLayout buttonLayout = new LinearLayout(this);
        buttonLayout.setOrientation(LinearLayout.HORIZONTAL);
        buttonLayout.setPadding(0, 24, 0, 0);
        
        // 取消按钮
        Button cancelButton = new Button(this);
        cancelButton.setText("取消");
        cancelButton.setTextColor(0xFF757575);
        cancelButton.setBackgroundColor(0xFFE0E0E0);
        LinearLayout.LayoutParams buttonParams = new LinearLayout.LayoutParams(0, 120, 1);
        buttonParams.setMargins(0, 0, 8, 0);
        cancelButton.setLayoutParams(buttonParams);
        cancelButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        buttonLayout.addView(cancelButton);
        
        // 保存按钮
        Button saveButton = new Button(this);
        saveButton.setText(isEditMode ? "更新员工" : "保存员工");
        saveButton.setTextColor(0xFFFFFFFF);
        saveButton.setBackgroundColor(0xFFFF6F00);
        LinearLayout.LayoutParams saveParams = new LinearLayout.LayoutParams(0, 120, 1);
        saveParams.setMargins(8, 0, 0, 0);
        saveButton.setLayoutParams(saveParams);
        saveButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                saveEmployee();
            }
        });
        buttonLayout.addView(saveButton);
        
        formLayout.addView(buttonLayout);
        mainLayout.addView(formLayout);
        scrollView.addView(mainLayout);
        setContentView(scrollView);
    }
    
    private void setupSpinners() {
        // 职位选项
        String[] positions = {"店长", "烘焙师", "裱花师", "收银员", "服务员", "清洁员"};
        ArrayAdapter<String> positionAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, positions);
        positionAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        positionSpinner.setAdapter(positionAdapter);
        
        // 部门选项
        String[] departments = {"管理部", "生产部", "销售部", "后勤部"};
        ArrayAdapter<String> departmentAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, departments);
        departmentAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        departmentSpinner.setAdapter(departmentAdapter);
    }
    
    private void saveEmployee() {
        String empId = empIdEdit.getText().toString().trim();
        String name = nameEdit.getText().toString().trim();
        String phone = phoneEdit.getText().toString().trim();
        String email = emailEdit.getText().toString().trim();
        String salaryStr = salaryEdit.getText().toString().trim();
        String hireDate = hireDateEdit.getText().toString().trim();
        String notes = notesEdit.getText().toString().trim();

        if (empId.isEmpty() || name.isEmpty()) {
            Toast.makeText(this, "请填写必填项目", Toast.LENGTH_SHORT).show();
            return;
        }

        double salary = 0.0;
        if (!salaryStr.isEmpty()) {
            try {
                salary = Double.parseDouble(salaryStr);
                if (salary < 0) {
                    Toast.makeText(this, "薪资不能为负数", Toast.LENGTH_SHORT).show();
                    return;
                }
            } catch (NumberFormatException e) {
                Toast.makeText(this, "请输入有效的薪资数字", Toast.LENGTH_SHORT).show();
                return;
            }
        }

        String position = positionSpinner.getSelectedItem().toString();
        String department = departmentSpinner.getSelectedItem().toString();
        boolean isActive = activeCheckBox.isChecked();
        
        if (isEditMode) {
            // 更新现有员工
            Employee employee = new Employee();
            employee.setId(employeeId);
            employee.setEmployeeId(empId);
            employee.setName(name);
            employee.setPosition(position);
            employee.setDepartment(department);
            employee.setPhone(phone);
            employee.setEmail(email);
            employee.setSalary(salary);
            employee.setHireDate(hireDate);
            employee.setNotes(notes);
            employee.setActive(isActive);
            employee.setUpdatedAt(System.currentTimeMillis());

            repository.updateEmployee(employee);
            Toast.makeText(this, "员工 \"" + name + "\" 更新成功！", Toast.LENGTH_LONG).show();
        } else {
            // 添加新员工
            Employee employee = new Employee(empId, name, position, department, phone);
            employee.setEmail(email);
            employee.setSalary(salary);
            employee.setHireDate(hireDate);
            employee.setNotes(notes);
            employee.setActive(isActive);
            repository.insertEmployee(employee);
            Toast.makeText(this, "员工 \"" + name + "\" 添加成功！", Toast.LENGTH_LONG).show();
        }
        
        finish();
    }
    
    private void loadEmployeeData() {
        employeeId = getIntent().getLongExtra("EMPLOYEE_ID", -1);
        String empId = getIntent().getStringExtra("EMPLOYEE_EMP_ID");
        String name = getIntent().getStringExtra("EMPLOYEE_NAME");
        String position = getIntent().getStringExtra("EMPLOYEE_POSITION");
        String department = getIntent().getStringExtra("EMPLOYEE_DEPARTMENT");
        String phone = getIntent().getStringExtra("EMPLOYEE_PHONE");
        String email = getIntent().getStringExtra("EMPLOYEE_EMAIL");
        double salary = getIntent().getDoubleExtra("EMPLOYEE_SALARY", 0.0);
        String hireDate = getIntent().getStringExtra("EMPLOYEE_HIRE_DATE");
        String notes = getIntent().getStringExtra("EMPLOYEE_NOTES");
        boolean isActive = getIntent().getBooleanExtra("EMPLOYEE_ACTIVE", true);

        empIdEdit.setText(empId);
        nameEdit.setText(name);
        phoneEdit.setText(phone);
        emailEdit.setText(email);
        if (salary > 0) {
            salaryEdit.setText(String.valueOf(salary));
        }
        hireDateEdit.setText(hireDate);
        notesEdit.setText(notes);
        activeCheckBox.setChecked(isActive);

        // 设置Spinner选择项
        setSpinnerSelection(positionSpinner, position);
        setSpinnerSelection(departmentSpinner, department);
    }
    
    private void setSpinnerSelection(Spinner spinner, String value) {
        if (value != null && spinner.getAdapter() != null) {
            for (int i = 0; i < spinner.getAdapter().getCount(); i++) {
                if (spinner.getAdapter().getItem(i).toString().equals(value)) {
                    spinner.setSelection(i);
                    break;
                }
            }
        }
    }
}
