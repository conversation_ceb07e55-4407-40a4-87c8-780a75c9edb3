package com.example.cakeshop;

import android.app.Application;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextWatcher;
import android.view.View;
import android.widget.*;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LifecycleRegistry;
import com.example.cakeshop.model.Order;
import com.example.cakeshop.model.Order.OrderStatus;
import com.example.cakeshop.model.Order.PaymentStatus;
import com.example.cakeshop.model.Order.PaymentMethod;
import com.example.cakeshop.model.OrderItem;
import com.example.cakeshop.model.Product;
import com.example.cakeshop.repository.CakeShopRepository;
import java.util.ArrayList;
import java.util.List;

public class EditOrderActivity extends android.app.Activity implements androidx.lifecycle.LifecycleOwner {
    private CakeShopRepository repository;
    private Spinner customerSpinner;
    private Spinner productSpinner;
    private EditText quantityEdit;
    private EditText notesEdit;
    private EditText deliveryDateEdit;
    private EditText deliveryAddressEdit;
    private EditText contactPhoneEdit;
    private Spinner statusSpinner;
    private Spinner paymentStatusSpinner;
    private Spinner paymentMethodSpinner;
    private TextView totalPriceText;
    private Button updateButton;
    private Button cancelButton;

    private Order currentOrder;
    private OrderItem currentOrderItem;
    private long orderId;
    private double[] productPrices;
    private androidx.lifecycle.LifecycleRegistry lifecycleRegistry;

    @Override
    public androidx.lifecycle.Lifecycle getLifecycle() {
        return lifecycleRegistry;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        lifecycleRegistry = new androidx.lifecycle.LifecycleRegistry(this);
        setContentView(R.layout.activity_edit_order);

        // 初始化Repository
        repository = new CakeShopRepository(getApplication());

        // 初始化UI组件
        initializeViews();

        // 设置按钮点击事件
        updateButton.setOnClickListener(v -> updateOrder());
        cancelButton.setOnClickListener(v -> finish());

        // 获取传入的订单ID
        orderId = getIntent().getLongExtra("ORDER_ID", -1);
        if (orderId == -1) {
            Toast.makeText(this, "无效的订单ID", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }

        // 设置数量变化监听
        quantityEdit.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void afterTextChanged(android.text.Editable s) {
                updateTotalPrice();
            }
        });

        // 产品选择变化监听
        productSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                updateTotalPrice();
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {}
        });

        // 加载订单数据
        loadOrderData();
    }
private void initializeViews() {
    quantityEdit = findViewById(R.id.quantity_edit);
    notesEdit = findViewById(R.id.notes_edit);
    deliveryDateEdit = findViewById(R.id.delivery_date_edit);
    deliveryAddressEdit = findViewById(R.id.delivery_address_edit);
    contactPhoneEdit = findViewById(R.id.contact_phone_edit);
    totalPriceText = findViewById(R.id.total_price_text);
    customerSpinner = findViewById(R.id.customer_spinner);
    productSpinner = findViewById(R.id.product_spinner);
    statusSpinner = findViewById(R.id.status_spinner);
    paymentStatusSpinner = findViewById(R.id.payment_status_spinner);
    paymentMethodSpinner = findViewById(R.id.payment_method_spinner);
    updateButton = findViewById(R.id.update_button);
    cancelButton = findViewById(R.id.cancel_button);

    List<String> products = new ArrayList<>();
    productPrices = new double[]{
        38.0,  // 草莓蛋糕
        42.0,  // 巧克力蛋糕
        45.0,  // 奶油蛋糕
        55.0   // 芝士蛋糕
    };
    products.add("草莓蛋糕 (38.0元)");
    products.add("巧克力蛋糕 (42.0元)");
    products.add("奶油蛋糕 (45.0元)");
    products.add("芝士蛋糕 (55.0元)");

    ArrayAdapter<String> productAdapter = new ArrayAdapter<>(this,
        android.R.layout.simple_spinner_item, products);
    productAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
    productSpinner.setAdapter(productAdapter);

    List<String> customers = new ArrayList<>();
    customers.add("张小明");
    customers.add("李小红");
    customers.add("王大华");
    customers.add("刘总");
    ArrayAdapter<String> customerAdapter = new ArrayAdapter<>(this,
        android.R.layout.simple_spinner_item, customers);
    customerAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
    customerSpinner.setAdapter(customerAdapter);

    ArrayAdapter<OrderStatus> statusAdapter = new ArrayAdapter<>(this,
        android.R.layout.simple_spinner_item,
        OrderStatus.values());
    statusAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
    statusSpinner.setAdapter(statusAdapter);

    ArrayAdapter<PaymentStatus> paymentStatusAdapter = new ArrayAdapter<>(this,
        android.R.layout.simple_spinner_item,
        PaymentStatus.values());
    paymentStatusAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
    paymentStatusSpinner.setAdapter(paymentStatusAdapter);

    ArrayAdapter<PaymentMethod> paymentMethodAdapter = new ArrayAdapter<>(this,
        android.R.layout.simple_spinner_item,
        PaymentMethod.values());
    paymentMethodAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
    paymentMethodSpinner.setAdapter(paymentMethodAdapter);
}
private void loadOrderData() {
        new Thread(() -> {
            try {
                // 同步获取订单数据
                currentOrder = repository.getOrderByIdSync(orderId);
                List<OrderItem> orderItems = repository.getOrderItemsByOrderIdSync(orderId);

                if (currentOrder == null || orderItems.isEmpty()) {
                    runOnUiThread(() -> {
                        Toast.makeText(this, "无法加载订单数据", Toast.LENGTH_SHORT).show();
                        finish();
                    });
                    return;
                }
                
                currentOrderItem = orderItems.get(0); // 获取第一个订单项

                runOnUiThread(() -> {
                    // 设置客户下拉框
                    int customerPosition = (int) currentOrder.getCustomerId() - 1; // 因为我们用的是固定列表
                    if (customerPosition >= 0 && customerPosition < customerSpinner.getAdapter().getCount()) {
                        customerSpinner.setSelection(customerPosition);
                    }

                    // 设置产品下拉框
                    if (currentOrderItem != null) {
                        int productPosition = (int) currentOrderItem.getProductId() - 1;
                        if (productPosition >= 0 && productPosition < productSpinner.getAdapter().getCount()) {
                            productSpinner.setSelection(productPosition);
                        }
                        // 设置数量
                        quantityEdit.setText(String.valueOf(currentOrderItem.getQuantity()));
                    }

                    // 设置订单状态
                    Order.OrderStatus[] orderStatuses = Order.OrderStatus.values();
                    for (int i = 0; i < orderStatuses.length; i++) {
                        if (orderStatuses[i].name().equals(currentOrder.getStatus())) {
                            statusSpinner.setSelection(i);
                            break;
                        }
                    }

                    // 设置支付状态
                    Order.PaymentStatus[] paymentStatuses = Order.PaymentStatus.values();
                    for (int i = 0; i < paymentStatuses.length; i++) {
                        if (paymentStatuses[i].name().equals(currentOrder.getPaymentStatus())) {
                            paymentStatusSpinner.setSelection(i);
                            break;
                        }
                    }

                    // 设置支付方式
                    Order.PaymentMethod[] paymentMethods = Order.PaymentMethod.values();
                    for (int i = 0; i < paymentMethods.length; i++) {
                        if (paymentMethods[i].name().equals(currentOrder.getPaymentMethod())) {
                            paymentMethodSpinner.setSelection(i);
                            break;
                        }
                    }

                    // 设置备注
                    notesEdit.setText(currentOrder.getSpecialInstructions());

                    // 设置交付日期
                    if (currentOrder.getDeliveryDate() > 0) {
                        java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault());
                        deliveryDateEdit.setText(sdf.format(new java.util.Date(currentOrder.getDeliveryDate())));
                    }

                    // 设置配送地址
                    deliveryAddressEdit.setText(currentOrder.getDeliveryAddress());

                    // 设置联系电话
                    contactPhoneEdit.setText(currentOrder.getContactPhone());

                    // 更新总价显示
                    updateTotalPrice();
                });
            } catch (Exception e) {
                runOnUiThread(() -> {
                    Toast.makeText(this, "加载订单数据失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                    finish();
                });
            }
        }).start();
    }

    private void updateTotalPrice() {
        try {
            String quantityStr = quantityEdit.getText().toString().trim();
            int quantity = quantityStr.isEmpty() ? 0 : Integer.parseInt(quantityStr);
            int productIndex = productSpinner.getSelectedItemPosition();

            if (productIndex >= 0 && productIndex < productPrices.length) {
                double unitPrice = productPrices[productIndex];
                double total = quantity * unitPrice;
                totalPriceText.setText(String.format("总价：%.2f元", total));
            }
        } catch (NumberFormatException e) {
            totalPriceText.setText("总价：0.00元");
        }
    }

    private void updateOrder() {
        // 获取表单数据
        int customerId = customerSpinner.getSelectedItemPosition() + 1; // 因为我们用的是固定列表，所以位置+1就是ID
        int productId = productSpinner.getSelectedItemPosition() + 1;

        // 验证数量
        String quantityStr = quantityEdit.getText().toString().trim();
        if (quantityStr.isEmpty()) {
            Toast.makeText(this, "请输入数量", Toast.LENGTH_SHORT).show();
            return;
        }
        int quantity = Integer.parseInt(quantityStr);
        if (quantity <= 0) {
            Toast.makeText(this, "数量必须大于0", Toast.LENGTH_SHORT).show();
            return;
        }

        // 获取其他字段
        String notes = notesEdit.getText().toString().trim();
        String deliveryDateStr = deliveryDateEdit.getText().toString().trim();
        String deliveryAddress = deliveryAddressEdit.getText().toString().trim();
        String contactPhone = contactPhoneEdit.getText().toString().trim();
        String status = OrderStatus.values()[statusSpinner.getSelectedItemPosition()].name();
        String paymentStatus = PaymentStatus.values()[paymentStatusSpinner.getSelectedItemPosition()].name();
        String paymentMethod = PaymentMethod.values()[paymentMethodSpinner.getSelectedItemPosition()].name();

        // 解析交付日期
        final long deliveryDate;
        if (!deliveryDateStr.isEmpty()) {
            try {
                java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault());
                deliveryDate = sdf.parse(deliveryDateStr).getTime();
            } catch (java.text.ParseException e) {
                Toast.makeText(this, "交付日期格式不正确，请使用 YYYY-MM-DD 格式", Toast.LENGTH_SHORT).show();
                return;
            }
        } else {
            deliveryDate = 0;
        }

        // 计算总价
        double unitPrice = productPrices[productSpinner.getSelectedItemPosition()];
        double totalPrice = quantity * unitPrice;

        new Thread(() -> {
            try {
                // 更新订单
                currentOrder.setCustomerId(customerId);
                currentOrder.setStatus(status);
                currentOrder.setPaymentStatus(paymentStatus);
                currentOrder.setPaymentMethod(paymentMethod);
                currentOrder.setSpecialInstructions(notes);
                currentOrder.setDeliveryDate(deliveryDate);
                currentOrder.setDeliveryAddress(deliveryAddress);
                currentOrder.setContactPhone(contactPhone);
                currentOrder.setTotalAmount(totalPrice);
                currentOrder.setUpdatedAt(System.currentTimeMillis());
                repository.updateOrder(currentOrder);

                // 更新订单项
                currentOrderItem.setProductId(productId);
                currentOrderItem.setQuantity(quantity);
                currentOrderItem.setUnitPrice(unitPrice);
                repository.updateOrderItem(currentOrderItem);

                runOnUiThread(() -> {
                    Toast.makeText(EditOrderActivity.this, "订单更新成功", Toast.LENGTH_SHORT).show();
                    setResult(RESULT_OK);
                    finish();
                });
            } catch (Exception e) {
                runOnUiThread(() -> {
                    Toast.makeText(EditOrderActivity.this, "更新失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                });
            }
        }).start();
    }
}