package com.example.cakeshop;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.*;
import com.example.cakeshop.database.CakeShopDatabase;
import com.example.cakeshop.model.User;
import com.example.cakeshop.utils.SessionManager;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class LoginActivity extends Activity {
    
    private EditText usernameEdit, passwordEdit;
    private Button loginButton, registerButton;
    private TextView titleText, subtitleText;
    private SessionManager sessionManager;
    private CakeShopDatabase database;
    private ExecutorService executor;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        sessionManager = new SessionManager(this);
        database = CakeShopDatabase.getDatabase(this);
        executor = Executors.newSingleThreadExecutor();
        
        // 检查是否已登录
        if (sessionManager.isLoggedIn()) {
            startMainActivity();
            return;
        }
        
        createLayout();
        initializeDefaultUsers();
    }
    
    private void createLayout() {
        LinearLayout mainLayout = new LinearLayout(this);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.setBackgroundColor(0xFFFCE4EC);
        mainLayout.setPadding(48, 80, 48, 80);
        mainLayout.setGravity(android.view.Gravity.CENTER);
        
        // Logo区域
        TextView logoText = new TextView(this);
        logoText.setText("🎂");
        logoText.setTextSize(64);
        logoText.setGravity(android.view.Gravity.CENTER);
        LinearLayout.LayoutParams logoParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        logoParams.setMargins(0, 0, 0, 24);
        logoText.setLayoutParams(logoParams);
        mainLayout.addView(logoText);
        
        // 标题
        titleText = new TextView(this);
        titleText.setText("甜蜜蛋糕店管理系统");
        titleText.setTextSize(24);
        titleText.setTextColor(0xFFD81B60);
        titleText.setTypeface(null, android.graphics.Typeface.BOLD);
        titleText.setGravity(android.view.Gravity.CENTER);
        LinearLayout.LayoutParams titleParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        titleParams.setMargins(0, 0, 0, 8);
        titleText.setLayoutParams(titleParams);
        mainLayout.addView(titleText);
        
        // 副标题
        subtitleText = new TextView(this);
        subtitleText.setText("请登录您的账户");
        subtitleText.setTextSize(16);
        subtitleText.setTextColor(0xFF8D6E63);
        subtitleText.setGravity(android.view.Gravity.CENTER);
        LinearLayout.LayoutParams subtitleParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        subtitleParams.setMargins(0, 0, 0, 48);
        subtitleText.setLayoutParams(subtitleParams);
        mainLayout.addView(subtitleText);
        
        // 登录表单容器
        LinearLayout formLayout = new LinearLayout(this);
        formLayout.setOrientation(LinearLayout.VERTICAL);
        formLayout.setBackgroundColor(0xFFFFFFFF);
        formLayout.setPadding(32, 32, 32, 32);
        LinearLayout.LayoutParams formParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        formParams.setMargins(0, 0, 0, 24);
        formLayout.setLayoutParams(formParams);
        
        LinearLayout.LayoutParams editParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 120);
        editParams.setMargins(0, 8, 0, 16);
        
        // 用户名
        TextView usernameLabel = new TextView(this);
        usernameLabel.setText("用户名");
        usernameLabel.setTextSize(16);
        usernameLabel.setTextColor(0xFF3E2723);
        usernameLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(usernameLabel);
        
        usernameEdit = new EditText(this);
        usernameEdit.setHint("请输入用户名");
        usernameEdit.setPadding(16, 16, 16, 16);
        usernameEdit.setBackgroundColor(0xFFF5F5F5);
        usernameEdit.setLayoutParams(editParams);
        formLayout.addView(usernameEdit);
        
        // 密码
        TextView passwordLabel = new TextView(this);
        passwordLabel.setText("密码");
        passwordLabel.setTextSize(16);
        passwordLabel.setTextColor(0xFF3E2723);
        passwordLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(passwordLabel);
        
        passwordEdit = new EditText(this);
        passwordEdit.setHint("请输入密码");
        passwordEdit.setInputType(android.text.InputType.TYPE_CLASS_TEXT | android.text.InputType.TYPE_TEXT_VARIATION_PASSWORD);
        passwordEdit.setPadding(16, 16, 16, 16);
        passwordEdit.setBackgroundColor(0xFFF5F5F5);
        passwordEdit.setLayoutParams(editParams);
        formLayout.addView(passwordEdit);
        
        // 登录按钮
        loginButton = new Button(this);
        loginButton.setText("登录");
        loginButton.setTextSize(18);
        loginButton.setTextColor(0xFFFFFFFF);
        loginButton.setBackgroundColor(0xFFD81B60);
        LinearLayout.LayoutParams buttonParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 120);
        buttonParams.setMargins(0, 24, 0, 0);
        loginButton.setLayoutParams(buttonParams);
        loginButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                performLogin();
            }
        });
        formLayout.addView(loginButton);

        // 注册按钮
        registerButton = new Button(this);
        registerButton.setText("新用户注册");
        registerButton.setTextSize(16);
        registerButton.setTextColor(0xFFD81B60);
        registerButton.setBackgroundColor(0xFFF5F5F5);
        LinearLayout.LayoutParams registerButtonParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 100);
        registerButtonParams.setMargins(0, 16, 0, 0);
        registerButton.setLayoutParams(registerButtonParams);
        registerButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(LoginActivity.this, RegisterActivity.class);
                startActivity(intent);
            }
        });
        formLayout.addView(registerButton);

        mainLayout.addView(formLayout);
        
        // 测试账户提示
        TextView hintText = new TextView(this);
        hintText.setText("测试账户：\n管理员 - admin/admin123\n员工 - employee/emp123");
        hintText.setTextSize(14);
        hintText.setTextColor(0xFF9E9E9E);
        hintText.setGravity(android.view.Gravity.CENTER);
        hintText.setBackgroundColor(0xFFF5F5F5);
        hintText.setPadding(16, 16, 16, 16);
        mainLayout.addView(hintText);
        
        setContentView(mainLayout);
    }
    
    private void initializeDefaultUsers() {
        executor.execute(new Runnable() {
            @Override
            public void run() {
                // 检查是否已有用户
                User existingAdmin = database.userDao().getUserByUsername("123123");
                if (existingAdmin == null) {
                    // 创建默认管理员账户 - 用户名和密码都是123123
                    User admin = new User("123123", "123123", "系统管理员", User.UserRole.ADMIN);
                    database.userDao().insertUser(admin);

                    // 创建默认员工账户
                    User employee = new User("employee", "emp123", "店员小王", User.UserRole.EMPLOYEE);
                    database.userDao().insertUser(employee);
                }
            }
        });
    }
    
    private void performLogin() {
        String username = usernameEdit.getText().toString().trim();
        String password = passwordEdit.getText().toString().trim();
        
        if (username.isEmpty() || password.isEmpty()) {
            Toast.makeText(this, "请输入用户名和密码", Toast.LENGTH_SHORT).show();
            return;
        }
        
        loginButton.setEnabled(false);
        loginButton.setText("登录中...");
        
        executor.execute(new Runnable() {
            @Override
            public void run() {
                User user = database.userDao().authenticateUser(username, password);
                
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        loginButton.setEnabled(true);
                        loginButton.setText("登录");
                        
                        if (user != null) {
                            // 更新最后登录时间
                            executor.execute(() -> database.userDao().updateLastLogin(user.getId(), System.currentTimeMillis()));
                            
                            // 创建会话
                            sessionManager.createLoginSession(user);
                            
                            Toast.makeText(LoginActivity.this, "欢迎回来，" + user.getFullName() + "！", Toast.LENGTH_SHORT).show();
                            startMainActivity();
                        } else {
                            Toast.makeText(LoginActivity.this, "用户名或密码错误", Toast.LENGTH_SHORT).show();
                        }
                    }
                });
            }
        });
    }
    
    private void startMainActivity() {
        Intent intent = new Intent(this, MainActivity.class);
        startActivity(intent);
        finish();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
}
