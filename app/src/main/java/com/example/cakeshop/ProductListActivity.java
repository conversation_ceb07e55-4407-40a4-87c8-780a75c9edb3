package com.example.cakeshop;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.Toast;
import android.graphics.drawable.GradientDrawable;
import com.example.cakeshop.model.Product;
import com.example.cakeshop.repository.CakeShopRepository;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import android.util.Log;

public class ProductListActivity extends Activity implements androidx.lifecycle.LifecycleOwner {
    private androidx.lifecycle.LifecycleRegistry lifecycleRegistry;
    private LinearLayout productContainer;
    private CakeShopRepository repository;
    private List<Product> currentProducts;
    private ExecutorService executor;

    @Override
    public androidx.lifecycle.Lifecycle getLifecycle() {
        return lifecycleRegistry;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        lifecycleRegistry = new androidx.lifecycle.LifecycleRegistry(this);
        executor = Executors.newSingleThreadExecutor(); 
        repository = new CakeShopRepository(getApplication());
        createLayout();
        loadProducts();
    }

    private void createLayout() {
        // 创建主容器 - 使用 FrameLayout 来支持浮动按钮
        FrameLayout mainContainer = new FrameLayout(this);
        mainContainer.setBackgroundColor(0xFFFCE4EC);

        ScrollView scrollView = new ScrollView(this);
        scrollView.setBackgroundColor(0xFFFCE4EC);

        LinearLayout mainLayout = new LinearLayout(this);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.setPadding(32, 32, 32, 100); // 增加底部padding为浮动按钮留空间

        // 标题（移除添加按钮）
        LinearLayout headerLayout = new LinearLayout(this);
        headerLayout.setOrientation(LinearLayout.HORIZONTAL);
        headerLayout.setPadding(0, 0, 0, 24);

        TextView titleText = new TextView(this);
        titleText.setText("🍰 产品管理");
        titleText.setTextSize(24);
        titleText.setTextColor(0xFFD81B60);
        titleText.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT));
        headerLayout.addView(titleText);
        mainLayout.addView(headerLayout);

        // 产品容器
        productContainer = new LinearLayout(this);
        productContainer.setOrientation(LinearLayout.VERTICAL);
        mainLayout.addView(productContainer);

        scrollView.addView(mainLayout);
        mainContainer.addView(scrollView);

        // 创建浮动添加按钮
        createFloatingAddButton(mainContainer);

        setContentView(mainContainer);
    }

    private void createFloatingAddButton(FrameLayout container) {
        // 创建浮动按钮
        Button fabButton = new Button(this);
        fabButton.setText("+");
        fabButton.setTextSize(24);
        fabButton.setTextColor(0xFFFFFFFF);

        // 创建圆形背景
        GradientDrawable background = new GradientDrawable();
        background.setShape(GradientDrawable.OVAL);
        background.setColor(0xFFD81B60); // 使用产品管理的主题色
        background.setStroke(0, 0xFFFFFFFF);
        fabButton.setBackground(background);

        // 设置按钮大小和位置
        int buttonSize = 168; // 56dp * 3 = 168px (假设密度为3)
        FrameLayout.LayoutParams fabParams = new FrameLayout.LayoutParams(buttonSize, buttonSize);
        fabParams.gravity = android.view.Gravity.BOTTOM | android.view.Gravity.END;
        fabParams.setMargins(0, 0, 48, 48); // 右下角边距
        fabButton.setLayoutParams(fabParams);

        // 添加阴影效果
        fabButton.setElevation(24f);
        fabButton.setStateListAnimator(null); // 移除默认动画

        // 设置点击事件
        fabButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(ProductListActivity.this, AddProductActivity.class);
                startActivity(intent);
            }
        });

        container.addView(fabButton);
    }

    private void loadProducts() {
        Log.d("ProductListActivity", "开始加载产品列表");
        repository.getAllProducts().observe(this, products -> {
            productContainer.removeAllViews();
            currentProducts = products;

            if (products == null || products.isEmpty()) {
                Log.d("ProductListActivity", "没有产品数据");
                TextView emptyText = new TextView(this);
                emptyText.setText("暂无产品数据\n点击右下角圆形按钮添加产品");
                emptyText.setTextSize(16);
                emptyText.setTextColor(0xFF757575);
                emptyText.setPadding(0, 50, 0, 50);
                emptyText.setGravity(android.view.Gravity.CENTER);
                productContainer.addView(emptyText);
                return;
            }

            Log.d("ProductListActivity", "加载到 " + products.size() + " 个产品");
            for (Product product : products) {
                try {
                    Log.d("ProductListActivity", "正在创建产品卡片: " + product.getName() +
                        ", 类别: " + product.getCategory() +
                        ", 尺寸: " + product.getSize() +
                        ", 口味: " + product.getFlavor());

                    LinearLayout productCard = createProductCard(product);
                    if (productCard != null) {
                        productContainer.addView(productCard);
                    }
                } catch (Exception e) {
                    Log.e("ProductListActivity", "创建产品卡片失败: " + product.getName(), e);
                }
            }
        });
    }

    private LinearLayout createProductCard(Product product) {
        LinearLayout card = new LinearLayout(this);
        card.setOrientation(LinearLayout.VERTICAL);
        card.setBackgroundColor(0xFFFFFFFF);
        card.setPadding(24, 24, 24, 24);

        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        );
        params.setMargins(0, 0, 0, 16);
        card.setLayoutParams(params);

        // 产品名称
        TextView nameText = new TextView(this);
        nameText.setText(product.getName());
        nameText.setTextSize(18);
        nameText.setTextColor(0xFF212121);
        nameText.setTypeface(null, android.graphics.Typeface.BOLD);
        card.addView(nameText);

        // 产品描述
        TextView descText = new TextView(this);
        descText.setText(product.getDescription());
        descText.setTextSize(14);
        descText.setTextColor(0xFF757575);
        descText.setPadding(0, 8, 0, 8);
        card.addView(descText);

        // 价格和类别
        TextView priceText = new TextView(this);
        String categoryDisplay = product.getCategory();
        try {
            Product.ProductCategory category = Product.ProductCategory.valueOf(product.getCategory());
            categoryDisplay = category.getDisplayName();
        } catch (Exception e) {
            // 如果转换失败，使用原始类别名称
        }
        priceText.setText(String.format("价格: ¥%.0f | 类别: %s", product.getPrice(), categoryDisplay));
        priceText.setTextSize(16);
        priceText.setTextColor(0xFFE91E63);
        priceText.setTypeface(null, android.graphics.Typeface.BOLD);
        card.addView(priceText);

        // 规格信息
        TextView specText = new TextView(this);
        String sizeDisplay = product.getSize();
        try {
            Product.CakeSize size = Product.CakeSize.valueOf(product.getSize());
            sizeDisplay = size.getDisplayName();
        } catch (Exception e) {
            // 如果转换失败，使用原始尺寸名称
        }

        String flavorDisplay = product.getFlavor();
        try {
            Product.CakeFlavor flavor = Product.CakeFlavor.valueOf(product.getFlavor());
            flavorDisplay = flavor.getDisplayName();
        } catch (Exception e) {
            // 如果转换失败，使用原始口味名称
        }

        specText.setText(String.format("尺寸: %s | 口味: %s | 制作时间: %d分钟",
            sizeDisplay, flavorDisplay, product.getPreparationTime()));
        specText.setTextSize(12);
        specText.setTextColor(0xFF9E9E9E);
        specText.setPadding(0, 8, 0, 0);
        card.addView(specText);

        // 操作按钮容器
        LinearLayout buttonLayout = new LinearLayout(this);
        buttonLayout.setOrientation(LinearLayout.HORIZONTAL);
        buttonLayout.setPadding(0, 16, 0, 0);

        // 编辑按钮
        Button editButton = new Button(this);
        editButton.setText("编辑");
        editButton.setTextSize(12);
        editButton.setTextColor(0xFFFFFFFF);
        editButton.setBackgroundColor(0xFF42A5F5);
        editButton.setPadding(16, 8, 16, 8);
        LinearLayout.LayoutParams editParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        editParams.setMargins(0, 0, 8, 0);
        editButton.setLayoutParams(editParams);
        editButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                editProduct(product);
            }
        });
        buttonLayout.addView(editButton);

        // 删除按钮
        Button deleteButton = new Button(this);
        deleteButton.setText("删除");
        deleteButton.setTextSize(12);
        deleteButton.setTextColor(0xFFFFFFFF);
        deleteButton.setBackgroundColor(0xFFFF5722);
        deleteButton.setPadding(16, 8, 16, 8);
        deleteButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                deleteProduct(product);
            }
        });
        buttonLayout.addView(deleteButton);

        card.addView(buttonLayout);

        return card;
    }

    private void editProduct(Product product) {
        Intent intent = new Intent(this, AddProductActivity.class);
        intent.putExtra("EDIT_MODE", true);
        intent.putExtra("PRODUCT_ID", product.getProductId());
        intent.putExtra("PRODUCT_NAME", product.getName());
        intent.putExtra("PRODUCT_DESC", product.getDescription());
        intent.putExtra("PRODUCT_PRICE", product.getPrice());
        intent.putExtra("PRODUCT_CATEGORY", product.getCategory());
        intent.putExtra("PRODUCT_SIZE", product.getSize());
        intent.putExtra("PRODUCT_FLAVOR", product.getFlavor());
        intent.putExtra("PRODUCT_PREP_TIME", product.getPreparationTime());
        intent.putExtra("PRODUCT_AVAILABLE", product.isAvailable());
        intent.putExtra("PRODUCT_CREATED_AT", product.getCreatedAt());
        startActivity(intent);
    }

    private void deleteProduct(Product product) {
        new android.app.AlertDialog.Builder(this)
            .setTitle("删除产品")
            .setMessage("确定要删除产品 \"" + product.getName() + "\" 吗？")
            .setPositiveButton("确定", new android.content.DialogInterface.OnClickListener() {
                @Override
                public void onClick(android.content.DialogInterface dialog, int which) {
                    Log.d("ProductListActivity", "开始删除产品: " + product.getName() + ", ID: " + product.getProductId());

                    // 使用新的回调机制删除产品
                    repository.deleteProduct(product, new CakeShopRepository.DeleteProductCallback() {
                        @Override
                        public void onSuccess() {
                            runOnUiThread(() -> {
                                android.widget.Toast.makeText(ProductListActivity.this,
                                    "产品 \"" + product.getName() + "\" 已删除",
                                    android.widget.Toast.LENGTH_SHORT).show();
                                Log.d("ProductListActivity", "产品删除成功");
                            });
                        }

                        @Override
                        public void onError(String message) {
                            runOnUiThread(() -> {
                                android.widget.Toast.makeText(ProductListActivity.this,
                                    message,
                                    android.widget.Toast.LENGTH_LONG).show();
                                Log.e("ProductListActivity", "产品删除失败: " + message);
                            });
                        }
                    });
                }
            })
            .setNegativeButton("取消", null)
            .show();
    }

    @Override
    protected void onStart() {
        super.onStart();
        lifecycleRegistry.handleLifecycleEvent(androidx.lifecycle.Lifecycle.Event.ON_START);
    }

    @Override
    protected void onResume() {
        super.onResume();
        lifecycleRegistry.handleLifecycleEvent(androidx.lifecycle.Lifecycle.Event.ON_RESUME);
    }

    @Override
    protected void onPause() {
        super.onPause();
        lifecycleRegistry.handleLifecycleEvent(androidx.lifecycle.Lifecycle.Event.ON_PAUSE);
    }

    @Override
    protected void onStop() {
        super.onStop();
        lifecycleRegistry.handleLifecycleEvent(androidx.lifecycle.Lifecycle.Event.ON_STOP);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        lifecycleRegistry.handleLifecycleEvent(androidx.lifecycle.Lifecycle.Event.ON_DESTROY);
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
}