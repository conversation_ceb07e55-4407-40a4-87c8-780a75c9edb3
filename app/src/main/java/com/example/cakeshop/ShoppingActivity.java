package com.example.cakeshop;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.*;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.example.cakeshop.database.CakeShopDatabase;
import com.example.cakeshop.model.Product;
import com.example.cakeshop.model.Cart;
import com.example.cakeshop.model.Customer;
import com.example.cakeshop.utils.SessionManager;
import java.util.List;
import java.util.ArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class ShoppingActivity extends Activity {
    
    private RecyclerView productsRecyclerView;
    private TextView cartCountText, cartTotalText;
    private Button viewCartButton;
    private EditText searchEdit;
    private Spinner categorySpinner;
    private ProductAdapter productAdapter;
    private List<Product> productList;
    private List<Product> filteredProductList;
    
    private CakeShopDatabase database;
    private SessionManager sessionManager;
    private ExecutorService executor;
    private long currentCustomerId;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        database = CakeShopDatabase.getDatabase(this);
        sessionManager = new SessionManager(this);
        executor = Executors.newSingleThreadExecutor();
        
        // 获取当前客户ID（这里简化处理，实际应该从登录信息获取）
        currentCustomerId = getIntent().getLongExtra("customer_id", 1);
        
        createLayout();
        loadProducts();
        updateCartInfo();
    }
    
    private void createLayout() {
        LinearLayout mainLayout = new LinearLayout(this);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.setBackgroundColor(0xFFF5F5F5);
        mainLayout.setPadding(16, 16, 16, 16);
        
        // 标题栏
        LinearLayout headerLayout = new LinearLayout(this);
        headerLayout.setOrientation(LinearLayout.HORIZONTAL);
        headerLayout.setBackgroundColor(0xFFD81B60);
        headerLayout.setPadding(16, 16, 16, 16);
        
        TextView titleText = new TextView(this);
        titleText.setText("🎂 甜蜜购物");
        titleText.setTextSize(20);
        titleText.setTextColor(0xFFFFFFFF);
        titleText.setTypeface(null, android.graphics.Typeface.BOLD);
        LinearLayout.LayoutParams titleParams = new LinearLayout.LayoutParams(
            0, LinearLayout.LayoutParams.WRAP_CONTENT, 1.0f);
        titleText.setLayoutParams(titleParams);
        headerLayout.addView(titleText);
        
        // 购物车信息
        LinearLayout cartInfoLayout = new LinearLayout(this);
        cartInfoLayout.setOrientation(LinearLayout.VERTICAL);
        cartInfoLayout.setGravity(android.view.Gravity.END);
        
        cartCountText = new TextView(this);
        cartCountText.setText("购物车: 0件");
        cartCountText.setTextColor(0xFFFFFFFF);
        cartCountText.setTextSize(12);
        cartInfoLayout.addView(cartCountText);
        
        cartTotalText = new TextView(this);
        cartTotalText.setText("总计: ¥0.00");
        cartTotalText.setTextColor(0xFFFFFFFF);
        cartTotalText.setTextSize(12);
        cartInfoLayout.addView(cartTotalText);
        
        headerLayout.addView(cartInfoLayout);
        mainLayout.addView(headerLayout);
        
        // 搜索和筛选区域
        LinearLayout searchLayout = new LinearLayout(this);
        searchLayout.setOrientation(LinearLayout.HORIZONTAL);
        searchLayout.setPadding(0, 16, 0, 16);
        
        searchEdit = new EditText(this);
        searchEdit.setHint("搜索产品...");
        searchEdit.setPadding(16, 16, 16, 16);
        searchEdit.setBackgroundColor(0xFFFFFFFF);
        LinearLayout.LayoutParams searchParams = new LinearLayout.LayoutParams(
            0, 120, 1.0f);
        searchParams.setMargins(0, 0, 16, 0);
        searchEdit.setLayoutParams(searchParams);
        searchLayout.addView(searchEdit);
        
        categorySpinner = new Spinner(this);
        String[] categories = {"全部分类", "生日蛋糕", "婚礼蛋糕", "纸杯蛋糕", "慕斯蛋糕", "芝士蛋糕", "巧克力蛋糕", "水果蛋糕"};
        ArrayAdapter<String> adapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, categories);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        categorySpinner.setAdapter(adapter);
        LinearLayout.LayoutParams spinnerParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.WRAP_CONTENT, 120);
        categorySpinner.setLayoutParams(spinnerParams);
        searchLayout.addView(categorySpinner);
        
        mainLayout.addView(searchLayout);
        
        // 产品列表
        productsRecyclerView = new RecyclerView(this);
        productsRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        LinearLayout.LayoutParams recyclerParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 0, 1.0f);
        productsRecyclerView.setLayoutParams(recyclerParams);
        mainLayout.addView(productsRecyclerView);
        
        // 底部按钮
        LinearLayout bottomLayout = new LinearLayout(this);
        bottomLayout.setOrientation(LinearLayout.HORIZONTAL);
        bottomLayout.setPadding(0, 16, 0, 0);
        
        viewCartButton = new Button(this);
        viewCartButton.setText("查看购物车");
        viewCartButton.setTextColor(0xFFFFFFFF);
        viewCartButton.setBackgroundColor(0xFFD81B60);
        LinearLayout.LayoutParams cartButtonParams = new LinearLayout.LayoutParams(
            0, 120, 1.0f);
        cartButtonParams.setMargins(0, 0, 8, 0);
        viewCartButton.setLayoutParams(cartButtonParams);
        viewCartButton.setOnClickListener(v -> {
            Intent intent = new Intent(this, CartActivity.class);
            intent.putExtra("customer_id", currentCustomerId);
            startActivity(intent);
        });
        bottomLayout.addView(viewCartButton);
        
        Button backButton = new Button(this);
        backButton.setText("返回");
        backButton.setTextColor(0xFF666666);
        backButton.setBackgroundColor(0xFFE0E0E0);
        LinearLayout.LayoutParams backButtonParams = new LinearLayout.LayoutParams(
            0, 120, 0.5f);
        backButtonParams.setMargins(8, 0, 0, 0);
        backButton.setLayoutParams(backButtonParams);
        backButton.setOnClickListener(v -> finish());
        bottomLayout.addView(backButton);
        
        mainLayout.addView(bottomLayout);
        setContentView(mainLayout);
    }
    
    private void loadProducts() {
        executor.execute(() -> {
            try {
                List<Product> products = database.productDao().getAllProductsSync();
                runOnUiThread(() -> {
                    productList = products;
                    filteredProductList = new ArrayList<>(products);
                    productAdapter = new ProductAdapter(filteredProductList);
                    productsRecyclerView.setAdapter(productAdapter);
                });
            } catch (Exception e) {
                runOnUiThread(() -> {
                    Toast.makeText(this, "加载产品失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                });
            }
        });
    }
    
    private void updateCartInfo() {
        executor.execute(() -> {
            try {
                int cartCount = database.cartDao().getCartItemCountSync(currentCustomerId);
                double cartTotal = database.cartDao().getCartTotalSync(currentCustomerId);
                
                runOnUiThread(() -> {
                    cartCountText.setText("购物车: " + cartCount + "件");
                    cartTotalText.setText("总计: ¥" + String.format("%.2f", cartTotal));
                });
            } catch (Exception e) {
                // 忽略错误，可能是数据库还没有数据
            }
        });
    }
    
    private void addToCart(Product product) {
        executor.execute(() -> {
            try {
                // 检查购物车中是否已有该产品
                Cart existingItem = database.cartDao().getCartItemByCustomerAndProduct(currentCustomerId, product.getProductId());
                
                if (existingItem != null) {
                    // 增加数量
                    existingItem.setQuantity(existingItem.getQuantity() + 1);
                    database.cartDao().updateCartItem(existingItem);
                } else {
                    // 添加新项目
                    Cart newItem = new Cart(currentCustomerId, product.getProductId(), 1, product.getPrice());
                    database.cartDao().insertCartItem(newItem);
                }
                
                runOnUiThread(() -> {
                    Toast.makeText(this, "已添加到购物车", Toast.LENGTH_SHORT).show();
                    updateCartInfo();
                });
            } catch (Exception e) {
                runOnUiThread(() -> {
                    Toast.makeText(this, "添加失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                });
            }
        });
    }
    
    // 产品适配器
    private class ProductAdapter extends RecyclerView.Adapter<ProductAdapter.ProductViewHolder> {
        private List<Product> products;
        
        public ProductAdapter(List<Product> products) {
            this.products = products;
        }
        
        @Override
        public ProductViewHolder onCreateViewHolder(android.view.ViewGroup parent, int viewType) {
            LinearLayout itemLayout = new LinearLayout(ShoppingActivity.this);
            itemLayout.setOrientation(LinearLayout.VERTICAL);
            itemLayout.setBackgroundColor(0xFFFFFFFF);
            itemLayout.setPadding(16, 16, 16, 16);
            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
            layoutParams.setMargins(0, 0, 0, 8);
            itemLayout.setLayoutParams(layoutParams);
            
            return new ProductViewHolder(itemLayout);
        }
        
        @Override
        public void onBindViewHolder(ProductViewHolder holder, int position) {
            Product product = products.get(position);
            holder.bind(product);
        }
        
        @Override
        public int getItemCount() {
            return products.size();
        }
        
        class ProductViewHolder extends RecyclerView.ViewHolder {
            private TextView nameText, priceText, descriptionText, categoryText;
            private Button addToCartButton;
            
            public ProductViewHolder(View itemView) {
                super(itemView);
                
                LinearLayout layout = (LinearLayout) itemView;
                
                // 产品信息布局
                LinearLayout infoLayout = new LinearLayout(ShoppingActivity.this);
                infoLayout.setOrientation(LinearLayout.HORIZONTAL);
                
                LinearLayout textLayout = new LinearLayout(ShoppingActivity.this);
                textLayout.setOrientation(LinearLayout.VERTICAL);
                LinearLayout.LayoutParams textParams = new LinearLayout.LayoutParams(
                    0, LinearLayout.LayoutParams.WRAP_CONTENT, 1.0f);
                textLayout.setLayoutParams(textParams);
                
                nameText = new TextView(ShoppingActivity.this);
                nameText.setTextSize(18);
                nameText.setTextColor(0xFF333333);
                nameText.setTypeface(null, android.graphics.Typeface.BOLD);
                textLayout.addView(nameText);
                
                priceText = new TextView(ShoppingActivity.this);
                priceText.setTextSize(16);
                priceText.setTextColor(0xFFD81B60);
                priceText.setTypeface(null, android.graphics.Typeface.BOLD);
                textLayout.addView(priceText);
                
                categoryText = new TextView(ShoppingActivity.this);
                categoryText.setTextSize(14);
                categoryText.setTextColor(0xFF666666);
                textLayout.addView(categoryText);
                
                descriptionText = new TextView(ShoppingActivity.this);
                descriptionText.setTextSize(14);
                descriptionText.setTextColor(0xFF888888);
                textLayout.addView(descriptionText);
                
                infoLayout.addView(textLayout);
                
                addToCartButton = new Button(ShoppingActivity.this);
                addToCartButton.setText("加入购物车");
                addToCartButton.setTextColor(0xFFFFFFFF);
                addToCartButton.setBackgroundColor(0xFFD81B60);
                LinearLayout.LayoutParams buttonParams = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT, 100);
                addToCartButton.setLayoutParams(buttonParams);
                infoLayout.addView(addToCartButton);
                
                layout.addView(infoLayout);
            }
            
            public void bind(Product product) {
                nameText.setText(product.getName());
                priceText.setText("¥" + String.format("%.2f", product.getPrice()));
                categoryText.setText(product.getCategory());
                descriptionText.setText(product.getDescription());
                
                addToCartButton.setOnClickListener(v -> addToCart(product));
            }
        }
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        updateCartInfo();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
}
