package com.example.cakeshop;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.*;
import com.example.cakeshop.database.CakeShopDatabase;
import com.example.cakeshop.model.*;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class CheckoutActivity extends Activity {
    
    private TextView totalAmountText, walletBalanceText, finalAmountText;
    private EditText deliveryAddressEdit, notesEdit;
    private Spinner paymentMethodSpinner;
    private CheckBox useWalletCheckBox;
    private Button confirmOrderButton, backButton;
    
    private CakeShopDatabase database;
    private ExecutorService executor;
    private long currentCustomerId;
    private double totalAmount;
    private double walletBalance = 0.0;
    private Customer currentCustomer;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        database = CakeShopDatabase.getDatabase(this);
        executor = Executors.newSingleThreadExecutor();
        currentCustomerId = getIntent().getLongExtra("customer_id", 1);
        totalAmount = getIntent().getDoubleExtra("total_amount", 0.0);
        
        createLayout();
        loadCustomerInfo();
    }
    
    private void createLayout() {
        ScrollView scrollView = new ScrollView(this);
        scrollView.setBackgroundColor(0xFFF5F5F5);
        
        LinearLayout mainLayout = new LinearLayout(this);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.setPadding(16, 16, 16, 16);
        
        // 标题栏
        LinearLayout headerLayout = new LinearLayout(this);
        headerLayout.setOrientation(LinearLayout.HORIZONTAL);
        headerLayout.setBackgroundColor(0xFFD81B60);
        headerLayout.setPadding(16, 16, 16, 16);
        
        TextView titleText = new TextView(this);
        titleText.setText("💳 订单结算");
        titleText.setTextSize(20);
        titleText.setTextColor(0xFFFFFFFF);
        titleText.setTypeface(null, android.graphics.Typeface.BOLD);
        headerLayout.addView(titleText);
        
        mainLayout.addView(headerLayout);
        
        // 订单信息卡片
        LinearLayout orderInfoCard = createCard();
        
        TextView orderInfoTitle = new TextView(this);
        orderInfoTitle.setText("📋 订单信息");
        orderInfoTitle.setTextSize(18);
        orderInfoTitle.setTextColor(0xFF333333);
        orderInfoTitle.setTypeface(null, android.graphics.Typeface.BOLD);
        orderInfoTitle.setPadding(0, 0, 0, 16);
        orderInfoCard.addView(orderInfoTitle);
        
        totalAmountText = new TextView(this);
        totalAmountText.setText("商品总额: ¥" + String.format("%.2f", totalAmount));
        totalAmountText.setTextSize(16);
        totalAmountText.setTextColor(0xFF666666);
        orderInfoCard.addView(totalAmountText);
        
        walletBalanceText = new TextView(this);
        walletBalanceText.setText("钱包余额: ¥0.00");
        walletBalanceText.setTextSize(16);
        walletBalanceText.setTextColor(0xFF4CAF50);
        orderInfoCard.addView(walletBalanceText);
        
        useWalletCheckBox = new CheckBox(this);
        useWalletCheckBox.setText("使用钱包余额支付");
        useWalletCheckBox.setTextSize(16);
        useWalletCheckBox.setOnCheckedChangeListener((buttonView, isChecked) -> updateFinalAmount());
        orderInfoCard.addView(useWalletCheckBox);
        
        finalAmountText = new TextView(this);
        finalAmountText.setText("实付金额: ¥" + String.format("%.2f", totalAmount));
        finalAmountText.setTextSize(18);
        finalAmountText.setTextColor(0xFFD81B60);
        finalAmountText.setTypeface(null, android.graphics.Typeface.BOLD);
        finalAmountText.setPadding(0, 16, 0, 0);
        orderInfoCard.addView(finalAmountText);
        
        mainLayout.addView(orderInfoCard);
        
        // 配送信息卡片
        LinearLayout deliveryCard = createCard();
        
        TextView deliveryTitle = new TextView(this);
        deliveryTitle.setText("🚚 配送信息");
        deliveryTitle.setTextSize(18);
        deliveryTitle.setTextColor(0xFF333333);
        deliveryTitle.setTypeface(null, android.graphics.Typeface.BOLD);
        deliveryTitle.setPadding(0, 0, 0, 16);
        deliveryCard.addView(deliveryTitle);
        
        TextView addressLabel = new TextView(this);
        addressLabel.setText("配送地址:");
        addressLabel.setTextSize(16);
        addressLabel.setTextColor(0xFF666666);
        deliveryCard.addView(addressLabel);
        
        deliveryAddressEdit = new EditText(this);
        deliveryAddressEdit.setHint("请输入配送地址");
        deliveryAddressEdit.setPadding(16, 16, 16, 16);
        deliveryAddressEdit.setBackgroundColor(0xFFF5F5F5);
        LinearLayout.LayoutParams addressParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 120);
        addressParams.setMargins(0, 8, 0, 16);
        deliveryAddressEdit.setLayoutParams(addressParams);
        deliveryCard.addView(deliveryAddressEdit);
        
        TextView notesLabel = new TextView(this);
        notesLabel.setText("备注信息:");
        notesLabel.setTextSize(16);
        notesLabel.setTextColor(0xFF666666);
        deliveryCard.addView(notesLabel);
        
        notesEdit = new EditText(this);
        notesEdit.setHint("请输入特殊要求或备注");
        notesEdit.setPadding(16, 16, 16, 16);
        notesEdit.setBackgroundColor(0xFFF5F5F5);
        LinearLayout.LayoutParams notesParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 120);
        notesParams.setMargins(0, 8, 0, 0);
        notesEdit.setLayoutParams(notesParams);
        deliveryCard.addView(notesEdit);
        
        mainLayout.addView(deliveryCard);
        
        // 支付方式卡片
        LinearLayout paymentCard = createCard();
        
        TextView paymentTitle = new TextView(this);
        paymentTitle.setText("💰 支付方式");
        paymentTitle.setTextSize(18);
        paymentTitle.setTextColor(0xFF333333);
        paymentTitle.setTypeface(null, android.graphics.Typeface.BOLD);
        paymentTitle.setPadding(0, 0, 0, 16);
        paymentCard.addView(paymentTitle);
        
        paymentMethodSpinner = new Spinner(this);
        String[] paymentMethods = {"现金支付", "银行卡支付", "支付宝", "微信支付"};
        ArrayAdapter<String> adapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, paymentMethods);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        paymentMethodSpinner.setAdapter(adapter);
        LinearLayout.LayoutParams spinnerParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 120);
        paymentMethodSpinner.setLayoutParams(spinnerParams);
        paymentCard.addView(paymentMethodSpinner);
        
        mainLayout.addView(paymentCard);
        
        // 底部按钮
        LinearLayout buttonLayout = new LinearLayout(this);
        buttonLayout.setOrientation(LinearLayout.HORIZONTAL);
        buttonLayout.setPadding(0, 24, 0, 0);
        
        backButton = new Button(this);
        backButton.setText("返回购物车");
        backButton.setTextColor(0xFF666666);
        backButton.setBackgroundColor(0xFFE0E0E0);
        LinearLayout.LayoutParams backParams = new LinearLayout.LayoutParams(
            0, 120, 1.0f);
        backParams.setMargins(0, 0, 8, 0);
        backButton.setLayoutParams(backParams);
        backButton.setOnClickListener(v -> finish());
        buttonLayout.addView(backButton);
        
        confirmOrderButton = new Button(this);
        confirmOrderButton.setText("确认下单");
        confirmOrderButton.setTextColor(0xFFFFFFFF);
        confirmOrderButton.setBackgroundColor(0xFFD81B60);
        LinearLayout.LayoutParams confirmParams = new LinearLayout.LayoutParams(
            0, 120, 1.5f);
        confirmParams.setMargins(8, 0, 0, 0);
        confirmOrderButton.setLayoutParams(confirmParams);
        confirmOrderButton.setOnClickListener(v -> confirmOrder());
        buttonLayout.addView(confirmOrderButton);
        
        mainLayout.addView(buttonLayout);
        
        scrollView.addView(mainLayout);
        setContentView(scrollView);
    }
    
    private LinearLayout createCard() {
        LinearLayout card = new LinearLayout(this);
        card.setOrientation(LinearLayout.VERTICAL);
        card.setBackgroundColor(0xFFFFFFFF);
        card.setPadding(16, 16, 16, 16);
        LinearLayout.LayoutParams cardParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        cardParams.setMargins(0, 8, 0, 8);
        card.setLayoutParams(cardParams);
        return card;
    }
    
    private void loadCustomerInfo() {
        executor.execute(() -> {
            try {
                // 这里简化处理，实际应该从数据库获取客户信息
                runOnUiThread(() -> {
                    walletBalance = 500.0; // 模拟钱包余额
                    walletBalanceText.setText("钱包余额: ¥" + String.format("%.2f", walletBalance));
                    deliveryAddressEdit.setText("默认地址"); // 可以从客户信息获取
                });
            } catch (Exception e) {
                runOnUiThread(() -> {
                    Toast.makeText(this, "加载客户信息失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                });
            }
        });
    }
    
    private void updateFinalAmount() {
        double finalAmount = totalAmount;
        if (useWalletCheckBox.isChecked()) {
            double walletDeduction = Math.min(walletBalance, totalAmount);
            finalAmount = totalAmount - walletDeduction;
        }
        finalAmountText.setText("实付金额: ¥" + String.format("%.2f", finalAmount));
    }
    
    private void confirmOrder() {
        String deliveryAddress = deliveryAddressEdit.getText().toString().trim();
        String notes = notesEdit.getText().toString().trim();
        String paymentMethod = paymentMethodSpinner.getSelectedItem().toString();
        
        if (deliveryAddress.isEmpty()) {
            Toast.makeText(this, "请输入配送地址", Toast.LENGTH_SHORT).show();
            return;
        }
        
        confirmOrderButton.setEnabled(false);
        confirmOrderButton.setText("处理中...");
        
        executor.execute(() -> {
            try {
                // 获取购物车商品
                List<Cart> cartItems = database.cartDao().getCartByCustomerSync(currentCustomerId);
                
                if (cartItems.isEmpty()) {
                    runOnUiThread(() -> {
                        Toast.makeText(this, "购物车是空的", Toast.LENGTH_SHORT).show();
                        confirmOrderButton.setEnabled(true);
                        confirmOrderButton.setText("确认下单");
                    });
                    return;
                }
                
                // 创建订单
                Order order = new Order();
                order.setCustomerId(currentCustomerId);
                order.setTotalAmount(totalAmount);
                order.setDeliveryAddress(deliveryAddress);
                order.setSpecialInstructions(notes);
                order.setStatus(Order.OrderStatus.PENDING.name());
                order.setPaymentStatus(Order.PaymentStatus.UNPAID.name());
                order.setPaymentMethod(paymentMethod);
                
                long orderId = database.orderDao().insertOrder(order);
                
                // 创建订单项
                for (Cart cartItem : cartItems) {
                    OrderItem orderItem = new OrderItem();
                    orderItem.setOrderId(orderId);
                    orderItem.setProductId(cartItem.getProductId());
                    orderItem.setQuantity(cartItem.getQuantity());
                    orderItem.setUnitPrice(cartItem.getUnitPrice());
                    orderItem.setCustomizations(cartItem.getCustomization());
                    
                    database.orderItemDao().insertOrderItem(orderItem);
                }
                
                // 处理钱包支付
                if (useWalletCheckBox.isChecked() && walletBalance > 0) {
                    double walletDeduction = Math.min(walletBalance, totalAmount);
                    // 这里应该更新客户钱包余额和创建交易记录
                    // 简化处理
                }
                
                // 清空购物车
                database.cartDao().clearCart(currentCustomerId);
                
                runOnUiThread(() -> {
                    Toast.makeText(this, "订单创建成功！", Toast.LENGTH_LONG).show();
                    
                    // 跳转到订单详情或返回主页
                    Intent intent = new Intent(this, MainActivity.class);
                    intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                    startActivity(intent);
                    finish();
                });
                
            } catch (Exception e) {
                runOnUiThread(() -> {
                    confirmOrderButton.setEnabled(true);
                    confirmOrderButton.setText("确认下单");
                    Toast.makeText(this, "创建订单失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                });
            }
        });
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
}