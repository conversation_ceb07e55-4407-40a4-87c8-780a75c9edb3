package com.example.cakeshop.database;

import androidx.lifecycle.LiveData;
import androidx.room.*;
import com.example.cakeshop.model.Transaction;
import java.util.List;

@Dao
public interface TransactionDao {
    
    // 查询操作
    @Query("SELECT * FROM transactions_java ORDER BY createdAt DESC")
    LiveData<List<Transaction>> getAllTransactions();
    
    @Query("SELECT * FROM transactions_java WHERE customerId = :customerId ORDER BY createdAt DESC")
    LiveData<List<Transaction>> getTransactionsByCustomer(long customerId);

    @Query("SELECT * FROM transactions_java WHERE customerId = :customerId ORDER BY createdAt DESC")
    List<Transaction> getTransactionsByCustomerSync(long customerId);

    @Query("SELECT * FROM transactions_java WHERE id = :id")
    LiveData<Transaction> getTransactionById(long id);

    @Query("SELECT * FROM transactions_java WHERE orderId = :orderId")
    LiveData<List<Transaction>> getTransactionsByOrder(long orderId);

    @Query("SELECT * FROM transactions_java WHERE transactionType = :type ORDER BY createdAt DESC")
    LiveData<List<Transaction>> getTransactionsByType(String type);

    @Query("SELECT * FROM transactions_java WHERE paymentMethod = :method ORDER BY createdAt DESC")
    LiveData<List<Transaction>> getTransactionsByPaymentMethod(String method);
    
    @Query("SELECT * FROM transactions_java WHERE createdAt BETWEEN :startTime AND :endTime ORDER BY createdAt DESC")
    LiveData<List<Transaction>> getTransactionsByDateRange(long startTime, long endTime);
    
    // 统计查询
    @Query("SELECT SUM(amount) FROM transactions_java WHERE customerId = :customerId AND transactionType = 'RECHARGE'")
    LiveData<Double> getTotalRechargeByCustomer(long customerId);

    @Query("SELECT SUM(amount) FROM transactions_java WHERE customerId = :customerId AND transactionType = 'PAYMENT'")
    LiveData<Double> getTotalPaymentByCustomer(long customerId);

    @Query("SELECT SUM(amount) FROM transactions_java WHERE transactionType = 'RECHARGE' AND createdAt BETWEEN :startTime AND :endTime")
    double getTotalRechargeInPeriod(long startTime, long endTime);

    @Query("SELECT SUM(amount) FROM transactions_java WHERE transactionType = 'PAYMENT' AND createdAt BETWEEN :startTime AND :endTime")
    double getTotalPaymentInPeriod(long startTime, long endTime);

    @Query("SELECT COUNT(*) FROM transactions_java WHERE customerId = :customerId")
    LiveData<Integer> getTransactionCountByCustomer(long customerId);
    
    // 插入操作
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insertTransaction(Transaction transaction);
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertTransactions(List<Transaction> transactions);
    
    // 更新操作
    @Update
    void updateTransaction(Transaction transaction);
    
    // 删除操作
    @Delete
    void deleteTransaction(Transaction transaction);
    
    @Query("DELETE FROM transactions_java WHERE id = :id")
    void deleteTransactionById(long id);
    
    @Query("DELETE FROM transactions_java WHERE customerId = :customerId")
    void deleteTransactionsByCustomer(long customerId);

    @Query("DELETE FROM transactions_java WHERE orderId = :orderId")
    void deleteTransactionsByOrder(long orderId);
}
