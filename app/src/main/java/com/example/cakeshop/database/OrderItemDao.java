package com.example.cakeshop.database;

import androidx.lifecycle.LiveData;
import androidx.room.*;
import com.example.cakeshop.model.OrderItem;
import java.util.List;

@Dao
public interface OrderItemDao {

    // 查询操作
    @Query("SELECT * FROM order_items WHERE order_id = :orderId")
    LiveData<List<OrderItem>> getOrderItems(long orderId);

    @Query("SELECT COUNT(*) FROM order_items WHERE product_id = :productId")
    int getOrderItemCountByProductId(long productId);

    /**
     * 同步获取指定订单的所有订单项
     * @param orderId 订单ID
     * @return 订单项列表
     */
    @Query("SELECT * FROM order_items WHERE order_id = :orderId")
    List<OrderItem> getOrderItemsByOrderIdSync(long orderId);

    @Query("SELECT * FROM order_items WHERE id = :id")
    LiveData<OrderItem> getOrderItemById(long id);

    @Query("SELECT * FROM order_items WHERE product_id = :productId")
    LiveData<List<OrderItem>> getOrderItemsByProduct(long productId);

    @Query("SELECT oi.* FROM order_items oi INNER JOIN orders o ON oi.order_id = o.id WHERE o.customer_id = :customerId")
    LiveData<List<OrderItem>> getOrderItemsByCustomer(long customerId);

    @Query("SELECT * FROM order_items WHERE subtotal >= :minAmount ORDER BY subtotal DESC")
    LiveData<List<OrderItem>> getHighValueOrderItems(double minAmount);

    // 插入操作
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insertOrderItem(OrderItem orderItem);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertOrderItems(List<OrderItem> orderItems);

    // 更新操作
    @Update
    void updateOrderItem(OrderItem orderItem);

    @Query("UPDATE order_items SET quantity = :quantity, subtotal = :subtotal WHERE id = :id")
    void updateOrderItemQuantity(long id, int quantity, double subtotal);

    @Query("UPDATE order_items SET unit_price = :unitPrice, subtotal = :subtotal WHERE id = :id")
    void updateOrderItemPrice(long id, double unitPrice, double subtotal);

    // 删除操作
    @Delete
    void deleteOrderItem(OrderItem orderItem);

    @Query("DELETE FROM order_items WHERE id = :id")
    void deleteOrderItemById(long id);

    @Query("DELETE FROM order_items WHERE order_id = :orderId")
    void deleteOrderItemsByOrderId(long orderId);

    @Query("DELETE FROM order_items WHERE product_id = :productId")
    void deleteOrderItemsByProductId(long productId);

    @Query("DELETE FROM order_items")
    void deleteAllOrderItems();

    // 统计操作
    @Query("SELECT COUNT(*) FROM order_items WHERE order_id = :orderId")
    LiveData<Integer> getOrderItemCount(long orderId);

    @Query("SELECT SUM(quantity) FROM order_items WHERE order_id = :orderId")
    LiveData<Integer> getTotalQuantityByOrder(long orderId);

    @Query("SELECT SUM(subtotal) FROM order_items WHERE order_id = :orderId")
    LiveData<Double> getOrderTotal(long orderId);

    @Query("SELECT SUM(quantity) FROM order_items WHERE product_id = :productId")
    LiveData<Integer> getTotalQuantityByProduct(long productId);

    @Query("SELECT COUNT(DISTINCT product_id) FROM order_items WHERE order_id = :orderId")
    LiveData<Integer> getUniqueProductCountByOrder(long orderId);

    @Query("SELECT AVG(subtotal) FROM order_items")
    LiveData<Double> getAverageOrderItemValue();

    // 复杂查询 - 最受欢迎的产品
    @Query("SELECT product_id AS productId, SUM(quantity) AS totalQuantity FROM order_items GROUP BY product_id ORDER BY totalQuantity DESC LIMIT :limit")
    LiveData<List<ProductSales>> getTopSellingProducts(int limit);

    // 内部类用于返回产品销售统计
    class ProductSales {
        public long productId;
        public int totalQuantity;

        public ProductSales(long productId, int totalQuantity) {
            this.productId = productId;
            this.totalQuantity = totalQuantity;
        }
    }
}