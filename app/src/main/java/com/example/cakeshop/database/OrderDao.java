package com.example.cakeshop.database;

import androidx.lifecycle.LiveData;
import androidx.room.*;
import com.example.cakeshop.model.Order;
import java.util.List;

@Dao
public interface OrderDao {

    // 查询操作
    @Query("SELECT * FROM orders ORDER BY order_date DESC")
    LiveData<List<Order>> getAllOrders();

    @Query("SELECT * FROM orders WHERE id = :id")
    LiveData<Order> getOrderById(long id);

    @Query("SELECT * FROM orders WHERE id = :orderId")
    Order getOrderByIdSync(long orderId);

    @Query("SELECT * FROM orders WHERE customer_id = :customerId ORDER BY order_date DESC")
    LiveData<List<Order>> getOrdersByCustomer(long customerId);

    @Query("SELECT * FROM orders WHERE status = :status ORDER BY order_date DESC")
    LiveData<List<Order>> getOrdersByStatus(String status);

    @Query("SELECT * FROM orders WHERE payment_status = :paymentStatus ORDER BY order_date DESC")
    LiveData<List<Order>> getOrdersByPaymentStatus(String paymentStatus);

    @Query("SELECT * FROM orders WHERE order_date BETWEEN :startDate AND :endDate ORDER BY order_date DESC")
    LiveData<List<Order>> getOrdersByDateRange(long startDate, long endDate);

    @Query("SELECT * FROM orders WHERE delivery_date BETWEEN :startDate AND :endDate ORDER BY delivery_date ASC")
    LiveData<List<Order>> getOrdersByDeliveryDateRange(long startDate, long endDate);

    @Query("SELECT * FROM orders WHERE order_number LIKE '%' || :searchQuery || '%'")
    LiveData<List<Order>> searchOrders(String searchQuery);

    @Query("SELECT * FROM orders WHERE final_amount BETWEEN :minAmount AND :maxAmount ORDER BY final_amount DESC")
    LiveData<List<Order>> getOrdersByAmountRange(double minAmount, double maxAmount);

    // 插入操作
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insertOrder(Order order);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertOrders(List<Order> orders);

    // 更新操作
    @Update
    void updateOrder(Order order);

    @Query("UPDATE orders SET status = :status WHERE id = :id")
    void updateOrderStatus(long id, String status);

    @Query("UPDATE orders SET payment_status = :paymentStatus WHERE id = :id")
    void updatePaymentStatus(long id, String paymentStatus);

    @Query("UPDATE orders SET delivery_date = :deliveryDate WHERE id = :id")
    void updateDeliveryDate(long id, long deliveryDate);

    // 删除操作
    @Delete
    void deleteOrder(Order order);

    @Query("DELETE FROM orders WHERE id = :id")
    void deleteOrderById(long id);

    @Query("DELETE FROM orders")
    void deleteAllOrders();

    // 统计操作
    @Query("SELECT COUNT(*) FROM orders")
    LiveData<Integer> getOrderCount();

    @Query("SELECT COUNT(*) FROM orders WHERE created_at >= :startTime AND created_at < :endTime")
    int getTodayOrdersCountSync(long startTime, long endTime);

    @Query("SELECT SUM(final_amount) FROM orders WHERE created_at >= :startTime AND created_at < :endTime")
    double getTodayRevenueSync(long startTime, long endTime);

    @Query("SELECT COUNT(*) FROM orders WHERE status = :status")
    LiveData<Integer> getOrderCountByStatus(String status);

    @Query("SELECT COUNT(*) FROM orders WHERE order_date BETWEEN :startDate AND :endDate")
    LiveData<Integer> getOrderCountByDateRange(long startDate, long endDate);

    @Query("SELECT SUM(final_amount) FROM orders WHERE payment_status = 'PAID'")
    LiveData<Double> getTotalRevenue();

    @Query("SELECT SUM(final_amount) FROM orders WHERE payment_status = 'PAID' AND order_date BETWEEN :startDate AND :endDate")
    LiveData<Double> getRevenueByDateRange(long startDate, long endDate);

    @Query("SELECT AVG(final_amount) FROM orders")
    LiveData<Double> getAverageOrderValue();

    @Query("SELECT DISTINCT status FROM orders ORDER BY status ASC")
    LiveData<List<String>> getAllOrderStatuses();

    @Query("SELECT DISTINCT payment_status FROM orders ORDER BY payment_status ASC")
    LiveData<List<String>> getAllPaymentStatuses();
}