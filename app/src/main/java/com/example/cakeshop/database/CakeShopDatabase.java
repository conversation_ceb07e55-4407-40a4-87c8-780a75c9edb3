package com.example.cakeshop.database;

import android.content.Context;
import android.database.Cursor;
import android.util.Log;
import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.room.TypeConverters;
import androidx.room.migration.Migration;
import androidx.sqlite.db.SupportSQLiteDatabase;
import com.example.cakeshop.model.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
@Database(
    entities = {
        Product.class,
        Customer.class,
        Order.class,
        OrderItem.class,
        Employee.class,
        Inventory.class,
        User.class,
        Cart.class,
        Transaction.class
    },
    version = 8,
    exportSchema = false
)
@TypeConverters({Converters.class})
public abstract class CakeShopDatabase extends RoomDatabase {

    private static volatile CakeShopDatabase INSTANCE;

    public abstract ProductDao productDao();
    public abstract CustomerDao customerDao();
    public abstract OrderDao orderDao();
    public abstract OrderItemDao orderItemDao();
    public abstract EmployeeDao employeeDao();
    public abstract InventoryDao inventoryDao();
    public abstract UserDao userDao();
    public abstract CartDao cartDao();
    public abstract TransactionDao transactionDao();

    private static RoomDatabase.Callback roomCallback = new RoomDatabase.Callback() {
        @Override
        public void onCreate(SupportSQLiteDatabase db) {
            super.onCreate(db);
            ExecutorService executor = Executors.newSingleThreadExecutor();
            executor.execute(() -> {
                try {
                    if (INSTANCE != null) {
                        ProductDao productDao = INSTANCE.productDao();

                        // 添加示例产品
                        Product[] products = {
                            new Product("经典生日蛋糕", "香草海绵蛋糕配奶油霜", 168.0,
                                Product.ProductCategory.BIRTHDAY_CAKE.name(),
                                Product.CakeSize.MEDIUM.name(),
                                Product.CakeFlavor.VANILLA.name(), 120),
                            new Product("巧克力慕斯蛋糕", "浓郁巧克力慕斯，口感丝滑", 198.0,
                                Product.ProductCategory.CHOCOLATE_CAKE.name(),
                                Product.CakeSize.MEDIUM.name(),
                                Product.CakeFlavor.CHOCOLATE.name(), 150),
                            new Product("草莓芝士蛋糕", "新鲜草莓配芝士蛋糕", 218.0,
                                Product.ProductCategory.CHEESECAKE.name(),
                                Product.CakeSize.MEDIUM.name(),
                                Product.CakeFlavor.STRAWBERRY.name(), 90),
                            new Product("迷你纸杯蛋糕", "精致小巧的纸杯蛋糕", 15.0,
                                Product.ProductCategory.CUPCAKE.name(),
                                Product.CakeSize.SMALL.name(),
                                Product.CakeFlavor.VANILLA.name(), 30),
                            new Product("豪华婚礼蛋糕", "三层豪华婚礼蛋糕", 888.0,
                                Product.ProductCategory.WEDDING_CAKE.name(),
                                Product.CakeSize.EXTRA_LARGE.name(),
                                Product.CakeFlavor.VANILLA.name(), 240),
                            new Product("抹茶红豆蛋糕", "日式抹茶配红豆", 188.0,
                                Product.ProductCategory.CUSTOM_CAKE.name(),
                                Product.CakeSize.MEDIUM.name(),
                                Product.CakeFlavor.MATCHA.name(), 100)
                        };

                        // 插入所有产品
                        for (Product product : products) {
                            productDao.insertProduct(product);
                        }
                    }
                } catch (Exception e) {
                    Log.e("CakeShopDatabase", "Error adding sample data: " + e.getMessage());
                }
            });
            executor.shutdown();
        }
    };

    public static CakeShopDatabase getDatabase(final Context context) {
        if (INSTANCE == null) {
            synchronized (CakeShopDatabase.class) {
                if (INSTANCE == null) {
                    INSTANCE = Room.databaseBuilder(
                        context.getApplicationContext(),
                        CakeShopDatabase.class,
                        "cakeshop_database"
                    )
                    .fallbackToDestructiveMigration()
                    .addCallback(roomCallback)
                    .addMigrations(new Migration(5, 6) {
                        @Override
                        public void migrate(SupportSQLiteDatabase database) {
                            try {
                                // 创建新的orders表
                                database.execSQL("CREATE TABLE IF NOT EXISTS orders (" +
                                    "id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, " +
                                    "customer_id INTEGER NOT NULL, " +
                                    "order_number TEXT, " +
                                    "order_date INTEGER, " +
                                    "delivery_date INTEGER, " +
                                    "status TEXT, " +
                                    "total_amount REAL NOT NULL, " +
                                    "discount_amount REAL NOT NULL, " +
                                    "final_amount REAL NOT NULL, " +
                                    "payment_status TEXT, " +
                                    "payment_method TEXT, " +
                                    "special_instructions TEXT, " +
                                    "delivery_address TEXT, " +
                                    "contact_phone TEXT, " +
                                    "created_at INTEGER, " +
                                    "updated_at INTEGER, " +
                                    "FOREIGN KEY(customer_id) REFERENCES customers(id) ON DELETE CASCADE)");

                                // 创建order_items表
                                database.execSQL("CREATE TABLE IF NOT EXISTS order_items (" +
                                    "id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, " +
                                    "order_id INTEGER NOT NULL, " +
                                    "product_id INTEGER NOT NULL, " +
                                    "quantity INTEGER NOT NULL, " +
                                    "unit_price REAL NOT NULL, " +
                                    "subtotal REAL NOT NULL, " +
                                    "customizations TEXT, " +
                                    "created_at INTEGER, " +
                                    "FOREIGN KEY(order_id) REFERENCES orders(id) ON DELETE CASCADE, " +
                                    "FOREIGN KEY(product_id) REFERENCES products(id) ON DELETE RESTRICT)");

                                // 创建索引
                                database.execSQL("CREATE INDEX IF NOT EXISTS index_orders_customer_id ON orders(customer_id)");
                                database.execSQL("CREATE INDEX IF NOT EXISTS index_order_items_order_id ON order_items(order_id)");
                                database.execSQL("CREATE INDEX IF NOT EXISTS index_order_items_product_id ON order_items(product_id)");
                            } catch (Exception e) {
                                Log.e("CakeShopDatabase", "Error during migration: " + e.getMessage());
                            }
                        }
                    }, new Migration(6, 7) {
                        @Override
                        public void migrate(SupportSQLiteDatabase database) {
                            try {
                                // 添加客户表的新字段
                                database.execSQL("ALTER TABLE customers_java ADD COLUMN walletBalance REAL DEFAULT 0.0");
                                database.execSQL("ALTER TABLE customers_java ADD COLUMN customerLevel TEXT DEFAULT 'BRONZE'");

                                // 创建购物车表
                                database.execSQL("CREATE TABLE IF NOT EXISTS cart_java (" +
                                    "id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, " +
                                    "customerId INTEGER NOT NULL, " +
                                    "productId INTEGER NOT NULL, " +
                                    "quantity INTEGER NOT NULL, " +
                                    "unitPrice REAL NOT NULL, " +
                                    "customization TEXT, " +
                                    "addedAt INTEGER NOT NULL, " +
                                    "FOREIGN KEY(customerId) REFERENCES customers_java(id) ON DELETE CASCADE, " +
                                    "FOREIGN KEY(productId) REFERENCES products(product_id) ON DELETE CASCADE)");

                                // 创建交易记录表
                                database.execSQL("CREATE TABLE IF NOT EXISTS transactions_java (" +
                                    "id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, " +
                                    "customerId INTEGER NOT NULL, " +
                                    "transactionType TEXT NOT NULL, " +
                                    "amount REAL NOT NULL, " +
                                    "balanceBefore REAL NOT NULL, " +
                                    "balanceAfter REAL NOT NULL, " +
                                    "description TEXT, " +
                                    "paymentMethod TEXT, " +
                                    "orderId INTEGER NOT NULL DEFAULT 0, " +
                                    "createdAt INTEGER NOT NULL, " +
                                    "FOREIGN KEY(customerId) REFERENCES customers_java(id) ON DELETE CASCADE)");

                                // 创建索引
                                database.execSQL("CREATE INDEX IF NOT EXISTS index_cart_java_customerId ON cart_java(customerId)");
                                database.execSQL("CREATE INDEX IF NOT EXISTS index_cart_java_productId ON cart_java(productId)");
                                database.execSQL("CREATE INDEX IF NOT EXISTS index_transactions_java_customerId ON transactions_java(customerId)");

                            } catch (Exception e) {
                                Log.e("CakeShopDatabase", "Error during migration 6->7: " + e.getMessage());
                            }
                        }
                    }, new Migration(7, 8) {
                        @Override
                        public void migrate(SupportSQLiteDatabase database) {
                            try {
                                // 创建新表（使用正确的表名）
                                database.execSQL("CREATE TABLE IF NOT EXISTS customers (" +
                                    "id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, " +
                                    "name TEXT, " +
                                    "phone TEXT, " +
                                    "email TEXT, " +
                                    "type TEXT, " +
                                    "walletBalance REAL DEFAULT 0.0, " +
                                    "customerLevel TEXT DEFAULT 'BRONZE')");

                                database.execSQL("CREATE TABLE IF NOT EXISTS cart (" +
                                    "id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, " +
                                    "customerId INTEGER NOT NULL, " +
                                    "productId INTEGER NOT NULL, " +
                                    "quantity INTEGER NOT NULL, " +
                                    "unitPrice REAL NOT NULL, " +
                                    "customization TEXT, " +
                                    "addedAt INTEGER NOT NULL, " +
                                    "FOREIGN KEY(customerId) REFERENCES customers(id) ON DELETE CASCADE, " +
                                    "FOREIGN KEY(productId) REFERENCES products(product_id) ON DELETE CASCADE)");

                                database.execSQL("CREATE TABLE IF NOT EXISTS transactions (" +
                                    "id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, " +
                                    "customerId INTEGER NOT NULL, " +
                                    "transactionType TEXT NOT NULL, " +
                                    "amount REAL NOT NULL, " +
                                    "balanceBefore REAL NOT NULL, " +
                                    "balanceAfter REAL NOT NULL, " +
                                    "description TEXT, " +
                                    "paymentMethod TEXT, " +
                                    "orderId INTEGER NOT NULL DEFAULT 0, " +
                                    "createdAt INTEGER NOT NULL, " +
                                    "FOREIGN KEY(customerId) REFERENCES customers(id) ON DELETE CASCADE)");

                                // 如果旧表存在，迁移数据
                                database.execSQL("INSERT OR IGNORE INTO customers SELECT * FROM customers_java");
                                database.execSQL("INSERT OR IGNORE INTO cart SELECT * FROM cart_java");
                                database.execSQL("INSERT OR IGNORE INTO transactions SELECT * FROM transactions_java");

                                // 删除旧表
                                database.execSQL("DROP TABLE IF EXISTS customers_java");
                                database.execSQL("DROP TABLE IF EXISTS cart_java");
                                database.execSQL("DROP TABLE IF EXISTS transactions_java");

                                // 创建新索引
                                database.execSQL("CREATE INDEX IF NOT EXISTS index_cart_customerId ON cart(customerId)");
                                database.execSQL("CREATE INDEX IF NOT EXISTS index_cart_productId ON cart(productId)");
                                database.execSQL("CREATE INDEX IF NOT EXISTS index_transactions_customerId ON transactions(customerId)");
                            } catch (Exception e) {
                                Log.e("CakeShopDatabase", "Error during migration 7->8: " + e.getMessage());
                            }
                        }
                    })
                    .build();
                }
            }
        }
        return INSTANCE;
    }

    public static void clearDatabase(Context context) {
        if (INSTANCE != null) {
            INSTANCE.close();
        }
        context.deleteDatabase("cakeshop_database");
        INSTANCE = null;
    }
}