package com.example.cakeshop.database;

import androidx.lifecycle.LiveData;
import androidx.room.*;
import com.example.cakeshop.model.Employee;
import java.util.List;

@Dao
public interface EmployeeDao {
    
    @Query("SELECT * FROM employees_java ORDER BY name ASC")
    LiveData<List<Employee>> getAllEmployees();
    
    @Query("SELECT * FROM employees_java WHERE id = :id")
    LiveData<Employee> getEmployeeById(long id);
    
    @Query("SELECT * FROM employees_java WHERE position = :position ORDER BY name ASC")
    LiveData<List<Employee>> getEmployeesByPosition(String position);
    
    @Query("SELECT * FROM employees_java WHERE department = :department ORDER BY name ASC")
    LiveData<List<Employee>> getEmployeesByDepartment(String department);
    
    @Query("SELECT * FROM employees_java WHERE isActive = 1 ORDER BY name ASC")
    LiveData<List<Employee>> getActiveEmployees();
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insertEmployee(Employee employee);
    
    @Update
    void updateEmployee(Employee employee);
    
    @Delete
    void deleteEmployee(Employee employee);
    
    @Query("DELETE FROM employees_java WHERE id = :id")
    void deleteEmployeeById(long id);
    
    @Query("SELECT COUNT(*) FROM employees_java WHERE isActive = 1")
    LiveData<Integer> getActiveEmployeeCount();
}
