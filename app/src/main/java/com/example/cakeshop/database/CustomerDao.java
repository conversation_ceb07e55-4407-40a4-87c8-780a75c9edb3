package com.example.cakeshop.database;

import androidx.lifecycle.LiveData;
import androidx.room.*;
import com.example.cakeshop.model.Customer;
import java.util.List;

@Dao
public interface CustomerDao {

    // 查询操作
    @Query("SELECT * FROM customers_java ORDER BY name ASC")
    LiveData<List<Customer>> getAllCustomers();

    @Query("SELECT name FROM customers_java")
    List<String> getAllCustomerNames();


    @Query("SELECT * FROM customers_java WHERE id = :id")
    LiveData<Customer> getCustomerById(long id);

    @Query("SELECT * FROM customers_java WHERE customerType = :customerType ORDER BY name ASC")
    LiveData<List<Customer>> getCustomersByType(String customerType);

    @Query("SELECT * FROM customers_java WHERE name LIKE '%' || :searchQuery || '%' OR phone LIKE '%' || :searchQuery || '%' OR email LIKE '%' || :searchQuery || '%'")
    LiveData<List<Customer>> searchCustomers(String searchQuery);

    @Query("SELECT * FROM customers_java WHERE phone = :phone")
    LiveData<Customer> getCustomerByPhone(String phone);

    @Query("SELECT * FROM customers_java WHERE email = :email")
    LiveData<Customer> getCustomerByEmail(String email);

    @Query("SELECT * FROM customers_java WHERE totalSpent >= :minAmount ORDER BY totalSpent DESC")
    LiveData<List<Customer>> getHighValueCustomers(double minAmount);

    @Query("SELECT * FROM customers_java WHERE loyaltyPoints >= :minPoints ORDER BY loyaltyPoints DESC")
    LiveData<List<Customer>> getCustomersByLoyaltyPoints(int minPoints);

    // 插入操作
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insertCustomer(Customer customer);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertCustomers(List<Customer> customers);

    // 更新操作
    @Update
    void updateCustomer(Customer customer);

    @Query("UPDATE customers_java SET totalOrders = :totalOrders, totalSpent = :totalSpent WHERE id = :id")
    void updateCustomerStats(long id, int totalOrders, double totalSpent);

    @Query("UPDATE customers_java SET loyaltyPoints = :loyaltyPoints WHERE id = :id")
    void updateLoyaltyPoints(long id, int loyaltyPoints);

    @Query("UPDATE customers_java SET customerType = :customerType WHERE id = :id")
    void updateCustomerType(long id, String customerType);

    // 删除操作
    @Delete
    void deleteCustomer(Customer customer);

    @Query("DELETE FROM customers_java WHERE id = :id")
    void deleteCustomerById(long id);

    @Query("DELETE FROM customers_java")
    void deleteAllCustomers();

    // 统计操作
    @Query("SELECT COUNT(*) FROM customers_java")
    LiveData<Integer> getCustomerCount();
    
    @Query("SELECT COUNT(*) FROM customers_java")
    int getTotalCustomersCountSync();

    @Query("SELECT COUNT(*) FROM customers_java WHERE customerType = :customerType")
    LiveData<Integer> getCustomerCountByType(String customerType);

    @Query("SELECT SUM(totalSpent) FROM customers_java")
    LiveData<Double> getTotalCustomerSpending();

    @Query("SELECT AVG(totalSpent) FROM customers_java")
    LiveData<Double> getAverageCustomerSpending();

    @Query("SELECT SUM(loyaltyPoints) FROM customers_java")
    LiveData<Integer> getTotalLoyaltyPoints();

    @Query("SELECT DISTINCT customerType FROM customers_java ORDER BY customerType ASC")
    LiveData<List<String>> getAllCustomerTypes();

    @Query("SELECT * FROM customers_java ORDER BY name ASC")
    List<Customer> getAllCustomersSync();
}