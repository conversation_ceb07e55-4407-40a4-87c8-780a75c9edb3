package com.example.cakeshop.database;

import androidx.lifecycle.LiveData;
import androidx.room.*;
import com.example.cakeshop.model.Cart;
import java.util.List;

@Dao
public interface CartDao {
    
    // 查询操作
    @Query("SELECT * FROM cart_java WHERE customerId = :customerId ORDER BY addedAt DESC")
    LiveData<List<Cart>> getCartByCustomer(long customerId);

    @Query("SELECT * FROM cart_java WHERE customerId = :customerId ORDER BY addedAt DESC")
    List<Cart> getCartByCustomerSync(long customerId);

    @Query("SELECT * FROM cart_java WHERE id = :id")
    LiveData<Cart> getCartItemById(long id);

    @Query("SELECT * FROM cart_java WHERE customerId = :customerId AND productId = :productId")
    Cart getCartItemByCustomerAndProduct(long customerId, long productId);

    @Query("SELECT COUNT(*) FROM cart_java WHERE customerId = :customerId")
    LiveData<Integer> getCartItemCount(long customerId);

    @Query("SELECT COUNT(*) FROM cart_java WHERE customerId = :customerId")
    int getCartItemCountSync(long customerId);

    @Query("SELECT SUM(quantity * unitPrice) FROM cart_java WHERE customerId = :customerId")
    LiveData<Double> getCartTotal(long customerId);

    @Query("SELECT SUM(quantity * unitPrice) FROM cart_java WHERE customerId = :customerId")
    double getCartTotalSync(long customerId);
    
    // 插入操作
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insertCartItem(Cart cart);
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertCartItems(List<Cart> carts);
    
    // 更新操作
    @Update
    void updateCartItem(Cart cart);
    
    @Query("UPDATE cart_java SET quantity = :quantity WHERE id = :id")
    void updateCartItemQuantity(long id, int quantity);
    
    @Query("UPDATE cart_java SET quantity = quantity + 1 WHERE id = :id")
    void increaseCartItemQuantity(long id);
    
    @Query("UPDATE cart_java SET quantity = quantity - 1 WHERE id = :id AND quantity > 1")
    void decreaseCartItemQuantity(long id);
    
    // 删除操作
    @Delete
    void deleteCartItem(Cart cart);
    
    @Query("DELETE FROM cart_java WHERE id = :id")
    void deleteCartItemById(long id);
    
    @Query("DELETE FROM cart_java WHERE customerId = :customerId")
    void clearCart(long customerId);

    @Query("DELETE FROM cart_java WHERE customerId = :customerId AND productId = :productId")
    void removeProductFromCart(long customerId, long productId);

    // 批量操作
    @Query("DELETE FROM cart_java WHERE customerId = :customerId AND id IN (:cartItemIds)")
    void deleteCartItems(long customerId, List<Long> cartItemIds);
}
