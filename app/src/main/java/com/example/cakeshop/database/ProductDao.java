package com.example.cakeshop.database;

import androidx.lifecycle.LiveData;
import androidx.room.*;
import com.example.cakeshop.model.Product;
import java.util.List;

@Dao
public interface ProductDao {

    // 同步查询操作
    @Query("SELECT * FROM products ORDER BY name ASC")
    List<Product> getAllProductsSync();

    // 异步查询操作
    @Query("SELECT * FROM products ORDER BY name ASC")
    LiveData<List<Product>> getAllProducts();
    @Query("SELECT * FROM products WHERE product_id = :productId")
    LiveData<Product> getProductById(long productId);

    @Query("SELECT * FROM products WHERE category = :category ORDER BY name ASC")
    LiveData<List<Product>> getProductsByCategory(String category);

    @Query("SELECT * FROM products WHERE is_available = 1 ORDER BY name ASC")
    LiveData<List<Product>> getAvailableProducts();

    // 搜索和过滤操作
    @Query("SELECT * FROM products WHERE name LIKE '%' || :searchQuery || '%' OR description LIKE '%' || :searchQuery || '%'")
    LiveData<List<Product>> searchProducts(String searchQuery);

    @Query("SELECT * FROM products WHERE price BETWEEN :minPrice AND :maxPrice ORDER BY price ASC")
    LiveData<List<Product>> getProductsByPriceRange(double minPrice, double maxPrice);

    // 插入操作
    @Insert
    default void insertProduct(Product product) {
        android.util.Log.d("ProductDao", "Inserting product: " + product.getName());
        insertProductImpl(product);
    }

    @Insert
    void insertProductImpl(Product product);

    @Insert
    long insertProductWithId(Product product);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertProducts(List<Product> products);

    // 更新操作
    @Update
    void updateProduct(Product product);

    @Query("UPDATE products SET is_available = :isAvailable WHERE product_id = :productId")
    void updateProductAvailability(long productId, boolean isAvailable);

    @Query("UPDATE products SET price = :price WHERE product_id = :productId")
    void updateProductPrice(long productId, double price);

    // 删除操作
    @Delete
    void deleteProduct(Product product);

    @Query("DELETE FROM products WHERE product_id = :id")
    void deleteProductById(long id);

    @Query("DELETE FROM products")
    default void deleteAllProducts() {
        android.util.Log.d("ProductDao", "Deleting all products");
        deleteAllProductsImpl();
    }

    @Query("DELETE FROM products") 
    void deleteAllProductsImpl();

    // 统计操作
    @Query("SELECT COUNT(*) FROM products")
    int getProductCount();

    @Query("SELECT COUNT(*) FROM products")
    LiveData<Integer> getProductCountLiveData();

    // Note: Low stock counting is handled by InventoryDao since products don't have stock fields
    // This method is kept for compatibility but should not be used
    @Query("SELECT 0")
    int getLowStockCountSync();

    @Query("SELECT COUNT(*) FROM products WHERE category = :category")
    LiveData<Integer> getProductCountByCategory(String category);

    @Query("SELECT COUNT(*) FROM products WHERE is_available = 1")
    LiveData<Integer> getAvailableProductCount();

    @Query("SELECT AVG(price) FROM products WHERE is_available = 1")
    LiveData<Double> getAveragePrice();

    @Query("SELECT DISTINCT category FROM products ORDER BY category ASC")
    LiveData<List<String>> getAllCategories();
}