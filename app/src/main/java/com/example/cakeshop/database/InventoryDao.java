package com.example.cakeshop.database;

import androidx.lifecycle.LiveData;
import androidx.room.*;
import com.example.cakeshop.model.Inventory;
import java.util.List;

@Dao
public interface InventoryDao {
    
    @Query("SELECT * FROM inventory_java ORDER BY itemName ASC")
    LiveData<List<Inventory>> getAllInventory();
    
    @Query("SELECT * FROM inventory_java WHERE id = :id")
    LiveData<Inventory> getInventoryById(long id);
    
    @Query("SELECT * FROM inventory_java WHERE category = :category ORDER BY itemName ASC")
    LiveData<List<Inventory>> getInventoryByCategory(String category);
    
    @Query("SELECT * FROM inventory_java WHERE currentStock <= minimumStock ORDER BY itemName ASC")
    LiveData<List<Inventory>> getLowStockItems();
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insertInventory(Inventory inventory);
    
    @Update
    void updateInventory(Inventory inventory);
    
    @Delete
    void deleteInventory(Inventory inventory);
    
    @Query("DELETE FROM inventory_java WHERE id = :id")
    void deleteInventoryById(long id);
    
    @Query("SELECT COUNT(*) FROM inventory_java WHERE currentStock <= minimumStock")
    LiveData<Integer> getLowStockCount();

    @Query("SELECT COUNT(*) FROM inventory_java WHERE currentStock <= minimumStock")
    int getLowStockCountSync();
}
