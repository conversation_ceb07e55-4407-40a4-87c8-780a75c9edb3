package com.example.cakeshop.database;

import androidx.lifecycle.LiveData;
import androidx.room.*;
import com.example.cakeshop.model.User;
import java.util.List;

@Dao
public interface UserDao {
    
    @Query("SELECT * FROM users_java WHERE isActive = 1 ORDER BY fullName ASC")
    LiveData<List<User>> getAllUsers();
    
    @Query("SELECT * FROM users_java WHERE id = :id")
    LiveData<User> getUserById(long id);
    
    @Query("SELECT * FROM users_java WHERE username = :username AND isActive = 1")
    User getUserByUsername(String username);
    
    @Query("SELECT * FROM users_java WHERE username = :username AND password = :password AND isActive = 1")
    User authenticateUser(String username, String password);
    
    @Query("SELECT * FROM users_java WHERE role = :role AND isActive = 1 ORDER BY fullName ASC")
    LiveData<List<User>> getUsersByRole(String role);
    
    @Query("SELECT COUNT(*) FROM users_java WHERE isActive = 1")
    LiveData<Integer> getActiveUserCount();
    
    @Query("SELECT COUNT(*) FROM users_java WHERE role = :role AND isActive = 1")
    LiveData<Integer> getUserCountByRole(String role);
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insertUser(User user);
    
    @Update
    void updateUser(User user);
    
    @Delete
    void deleteUser(User user);
    
    @Query("UPDATE users_java SET isActive = 0 WHERE id = :id")
    void deactivateUser(long id);
    
    @Query("UPDATE users_java SET lastLoginAt = :loginTime WHERE id = :id")
    void updateLastLogin(long id, long loginTime);
    
    @Query("UPDATE users_java SET password = :newPassword WHERE id = :id")
    void updatePassword(long id, String newPassword);
}
