package com.example.cakeshop;

import android.app.Activity;
import android.os.Bundle;
import android.view.View;
import android.widget.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import com.example.cakeshop.model.Order;
import com.example.cakeshop.model.OrderItem;
import com.example.cakeshop.database.CakeShopDatabase;
import com.example.cakeshop.repository.CakeShopRepository;
import java.util.ArrayList;
import java.util.List;
import com.example.cakeshop.model.Order.OrderStatus;
import com.example.cakeshop.model.Order.PaymentStatus;
public class AddOrderActivity extends Activity {
    
    private Spinner customerSpinner, productSpinner, statusSpinner;
    private EditText quantityEdit, notesEdit;
    private TextView totalPriceText, orderDateText;
    private double[] productPrices = {168.0, 198.0, 218.0, 15.0, 888.0, 188.0};
    private CakeShopRepository repository;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        repository = new CakeShopRepository(getApplication());
        createLayout();
        setupSpinners();
        updateOrderDate();
    }
    
    private void createLayout() {
        ScrollView scrollView = new ScrollView(this);
        scrollView.setBackgroundColor(0xFFFCE4EC);
        
        LinearLayout mainLayout = new LinearLayout(this);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.setPadding(32, 32, 32, 32);
        
        // 标题
        TextView titleText = new TextView(this);
        titleText.setText("📋 新建订单");
        titleText.setTextSize(24);
        titleText.setTextColor(0xFF42A5F5);
        titleText.setPadding(0, 0, 0, 32);
        mainLayout.addView(titleText);
        
        // 表单容器
        LinearLayout formLayout = new LinearLayout(this);
        formLayout.setOrientation(LinearLayout.VERTICAL);
        formLayout.setBackgroundColor(0xFFFFFFFF);
        formLayout.setPadding(24, 24, 24, 24);
        
        LinearLayout.LayoutParams editParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 120);
        editParams.setMargins(0, 8, 0, 16);
        
        // 订单日期
        TextView dateLabel = new TextView(this);
        dateLabel.setText("订单日期");
        dateLabel.setTextSize(16);
        dateLabel.setTextColor(0xFF3E2723);
        dateLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(dateLabel);
        
        orderDateText = new TextView(this);
        orderDateText.setTextSize(16);
        orderDateText.setTextColor(0xFF8D6E63);
        orderDateText.setPadding(16, 16, 16, 16);
        orderDateText.setBackgroundColor(0xFFF5F5F5);
        orderDateText.setLayoutParams(editParams);
        formLayout.addView(orderDateText);
        
        // 选择客户
        TextView customerLabel = new TextView(this);
        customerLabel.setText("选择客户 *");
        customerLabel.setTextSize(16);
        customerLabel.setTextColor(0xFF3E2723);
        customerLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(customerLabel);
        
        customerSpinner = new Spinner(this);
        customerSpinner.setPadding(16, 16, 16, 16);
        customerSpinner.setBackgroundColor(0xFFF5F5F5);
        customerSpinner.setLayoutParams(editParams);
        formLayout.addView(customerSpinner);
        
        // 选择产品
        TextView productLabel = new TextView(this);
        productLabel.setText("选择产品 *");
        productLabel.setTextSize(16);
        productLabel.setTextColor(0xFF3E2723);
        productLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(productLabel);
        
        productSpinner = new Spinner(this);
        productSpinner.setPadding(16, 16, 16, 16);
        productSpinner.setBackgroundColor(0xFFF5F5F5);
        productSpinner.setLayoutParams(editParams);
        productSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                updateTotalPrice();
            }
            
            @Override
            public void onNothingSelected(AdapterView<?> parent) {}
        });
        formLayout.addView(productSpinner);
        
        // 数量
        TextView quantityLabel = new TextView(this);
        quantityLabel.setText("数量 *");
        quantityLabel.setTextSize(16);
        quantityLabel.setTextColor(0xFF3E2723);
        quantityLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(quantityLabel);
        
        quantityEdit = new EditText(this);
        quantityEdit.setHint("请输入数量");
        quantityEdit.setText("1");
        quantityEdit.setInputType(android.text.InputType.TYPE_CLASS_NUMBER);
        quantityEdit.setPadding(16, 16, 16, 16);
        quantityEdit.setBackgroundColor(0xFFF5F5F5);
        quantityEdit.setLayoutParams(editParams);
        quantityEdit.addTextChangedListener(new android.text.TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}
            
            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                updateTotalPrice();
            }
            
            @Override
            public void afterTextChanged(android.text.Editable s) {}
        });
        formLayout.addView(quantityEdit);
        
        // 总价
        TextView priceLabel = new TextView(this);
        priceLabel.setText("总价");
        priceLabel.setTextSize(16);
        priceLabel.setTextColor(0xFF3E2723);
        priceLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(priceLabel);
        
        totalPriceText = new TextView(this);
        totalPriceText.setText("¥0.00");
        totalPriceText.setTextSize(18);
        totalPriceText.setTextColor(0xFFD81B60);
        totalPriceText.setTypeface(null, android.graphics.Typeface.BOLD);
        totalPriceText.setPadding(16, 16, 16, 16);
        totalPriceText.setBackgroundColor(0xFFF5F5F5);
        totalPriceText.setLayoutParams(editParams);
        formLayout.addView(totalPriceText);
        
        // 订单状态
        TextView statusLabel = new TextView(this);
        statusLabel.setText("订单状态");
        statusLabel.setTextSize(16);
        statusLabel.setTextColor(0xFF3E2723);
        statusLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(statusLabel);
        
        statusSpinner = new Spinner(this);
        statusSpinner.setPadding(16, 16, 16, 16);
        statusSpinner.setBackgroundColor(0xFFF5F5F5);
        statusSpinner.setLayoutParams(editParams);
        formLayout.addView(statusSpinner);
        
        // 备注
        TextView notesLabel = new TextView(this);
        notesLabel.setText("备注信息");
        notesLabel.setTextSize(16);
        notesLabel.setTextColor(0xFF3E2723);
        notesLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(notesLabel);
        
        notesEdit = new EditText(this);
        notesEdit.setHint("请输入备注信息");
        notesEdit.setPadding(16, 16, 16, 16);
        notesEdit.setBackgroundColor(0xFFF5F5F5);
        notesEdit.setLayoutParams(editParams);
        formLayout.addView(notesEdit);
        
        // 按钮容器
        LinearLayout buttonLayout = new LinearLayout(this);
        buttonLayout.setOrientation(LinearLayout.HORIZONTAL);
        buttonLayout.setPadding(0, 24, 0, 0);
        
        // 取消按钮
        Button cancelButton = new Button(this);
        cancelButton.setText("取消");
        cancelButton.setTextColor(0xFF757575);
        cancelButton.setBackgroundColor(0xFFE0E0E0);
        LinearLayout.LayoutParams buttonParams = new LinearLayout.LayoutParams(0, 120, 1);
        buttonParams.setMargins(0, 0, 8, 0);
        cancelButton.setLayoutParams(buttonParams);
        cancelButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        buttonLayout.addView(cancelButton);
        
        // 保存按钮
        Button saveButton = new Button(this);
        saveButton.setText("创建订单");
        saveButton.setTextColor(0xFFFFFFFF);
        saveButton.setBackgroundColor(0xFF42A5F5);
        LinearLayout.LayoutParams saveParams = new LinearLayout.LayoutParams(0, 120, 1);
        saveParams.setMargins(8, 0, 0, 0);
        saveButton.setLayoutParams(saveParams);
        saveButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                saveOrder();
            }
        });
        buttonLayout.addView(saveButton);
        
        formLayout.addView(buttonLayout);
        mainLayout.addView(formLayout);
        scrollView.addView(mainLayout);
        setContentView(scrollView);
    }
    
    private void setupSpinners() {
        // 客户选项
        String[] customers = {"张小明 (VIP客户)", "李小红 (普通客户)", "王大华 (高级客户)", "刘总 (企业客户)"};
        ArrayAdapter<String> customerAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, customers);
        customerAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        customerSpinner.setAdapter(customerAdapter);
        
        // 产品选项
        String[] products = {
            "经典生日蛋糕 (¥168)", "巧克力慕斯蛋糕 (¥198)", "草莓芝士蛋糕 (¥218)",
            "迷你纸杯蛋糕 (¥15)", "豪华婚礼蛋糕 (¥888)", "抹茶红豆蛋糕 (¥188)"
        };
        ArrayAdapter<String> productAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, products);
        productAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        productSpinner.setAdapter(productAdapter);
        
        // 状态选项
        String[] statuses = {"待确认", "已确认", "制作中", "待取货", "已完成", "已取消"};
        ArrayAdapter<String> statusAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, statuses);
        statusAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        statusSpinner.setAdapter(statusAdapter);
        statusSpinner.setSelection(1); // 默认选择"已确认"
    }
    
    private void updateOrderDate() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日 HH:mm", Locale.CHINESE);
        orderDateText.setText(dateFormat.format(new Date()));
    }
    
    private void updateTotalPrice() {
        try {
            int productIndex = productSpinner.getSelectedItemPosition();
            String quantityStr = quantityEdit.getText().toString().trim();
            
            if (!quantityStr.isEmpty() && productIndex >= 0 && productIndex < productPrices.length) {
                int quantity = Integer.parseInt(quantityStr);
                double totalPrice = productPrices[productIndex] * quantity;
                totalPriceText.setText(String.format(Locale.CHINESE, "¥%.2f", totalPrice));
            }
        } catch (NumberFormatException e) {
            totalPriceText.setText("¥0.00");
        }
    }
    
    private void saveOrder() {
        String quantityStr = quantityEdit.getText().toString().trim();

        if (quantityStr.isEmpty()) {
            Toast.makeText(this, "请填写数量", Toast.LENGTH_SHORT).show();
            return;
        }

        try {
            int quantity = Integer.parseInt(quantityStr);
            if (quantity <= 0) {
                Toast.makeText(this, "数量必须大于0", Toast.LENGTH_SHORT).show();
                return;
            }

            String customerStr = (String) customerSpinner.getSelectedItem();
            String productStr = (String) productSpinner.getSelectedItem();
            String orderNumber = "ORD" + System.currentTimeMillis() % 10000;
            String status = (String) statusSpinner.getSelectedItem();
            String notes = notesEdit.getText().toString().trim();

            // 解析客户名和ID (这里暂时用1-4作为示例客户ID)
            String customerName = customerStr.split(" ")[0];
            long customerId;
            switch (customerName) {
                case "张小明":
                    customerId = 1L;
                    break;
                case "李小红":
                    customerId = 2L;
                    break;
                case "王大华":
                    customerId = 3L;
                    break;
                case "刘总":
                    customerId = 4L;
                    break;
                default:
                    customerId = 1L;
                    break;
            }

            // 解析产品名和ID (这里暂时用1-6作为示例产品ID)
            String productName = productStr.substring(0, productStr.lastIndexOf(" ("));
            int productIndex = productSpinner.getSelectedItemPosition();
            long productId = productIndex + 1; // 产品ID从1开始
            double productPrice = productPrices[productIndex];
            double totalPrice = productPrice * quantity;

            // 创建订单对象
            Order order = new Order();
            order.setOrderNumber(orderNumber);
            order.setCustomerId(customerId);
            order.setTotalAmount(totalPrice);
            order.setFinalAmount(totalPrice);
            order.setStatus(OrderStatus.valueOf("CONFIRMED").name());
            order.setSpecialInstructions(notes);
            order.setPaymentStatus(PaymentStatus.UNPAID.name());
            order.setOrderDate(System.currentTimeMillis());

            // 创建订单项
            OrderItem item = new OrderItem();
            item.setProductId(productId);
            item.setQuantity(quantity);
            item.setUnitPrice(productPrice);
            item.setSubtotal(productPrice * quantity); // 计算小计

            // 保存订单和订单项
            new Thread(() -> {
                try {
                    // 保存订单
                    long orderId = repository.insertOrderSync(order);

                    // 设置订单项的订单ID并保存
                    item.setOrderId(orderId);
                    repository.insertOrderItem(item);

                    runOnUiThread(() -> {
                        Toast.makeText(AddOrderActivity.this, 
                            "订单 " + orderNumber + " 创建成功！\n客户: " + customerName, 
                            Toast.LENGTH_LONG).show();
                        finish();
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                    runOnUiThread(() -> {
                        Toast.makeText(AddOrderActivity.this, 
                            "订单创建失败：" + e.getMessage(), 
                            Toast.LENGTH_LONG).show();
                    });
                }
            }).start();

        } catch (NumberFormatException e) {
            Toast.makeText(this, "请输入正确的数量", Toast.LENGTH_SHORT).show();
        }
    }
}