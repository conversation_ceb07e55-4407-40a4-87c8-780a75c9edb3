package com.example.cakeshop;

import android.app.Activity;
import android.os.Bundle;
import android.view.View;
import android.widget.*;
import com.example.cakeshop.model.Inventory;
import com.example.cakeshop.repository.CakeShopRepository;

public class AddInventoryActivity extends Activity {
    
    private EditText nameEdit, currentStockEdit, minStockEdit, unitCostEdit;
    private Spinner categorySpinner, unitSpinner;
    private CakeShopRepository repository;
    private boolean isEditMode = false;
    private long inventoryId = -1;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        repository = new CakeShopRepository(getApplication());
        isEditMode = getIntent().getBooleanExtra("EDIT_MODE", false);
        
        createLayout();
        setupSpinners();
        
        if (isEditMode) {
            loadInventoryData();
        }
    }
    
    private void createLayout() {
        ScrollView scrollView = new ScrollView(this);
        scrollView.setBackgroundColor(0xFFFCE4EC);
        
        LinearLayout mainLayout = new LinearLayout(this);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.setPadding(32, 32, 32, 32);
        
        // 标题
        TextView titleText = new TextView(this);
        titleText.setText(isEditMode ? "📦 编辑库存" : "📦 添加新库存");
        titleText.setTextSize(24);
        titleText.setTextColor(0xFFFF5722);
        titleText.setPadding(0, 0, 0, 32);
        mainLayout.addView(titleText);
        
        // 表单容器
        LinearLayout formLayout = new LinearLayout(this);
        formLayout.setOrientation(LinearLayout.VERTICAL);
        formLayout.setBackgroundColor(0xFFFFFFFF);
        formLayout.setPadding(24, 24, 24, 24);
        
        LinearLayout.LayoutParams editParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 120);
        editParams.setMargins(0, 8, 0, 16);
        
        // 物料名称
        TextView nameLabel = new TextView(this);
        nameLabel.setText("物料名称 *");
        nameLabel.setTextSize(16);
        nameLabel.setTextColor(0xFF3E2723);
        nameLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(nameLabel);
        
        nameEdit = new EditText(this);
        nameEdit.setHint("请输入物料名称");
        nameEdit.setPadding(16, 16, 16, 16);
        nameEdit.setBackgroundColor(0xFFF5F5F5);
        nameEdit.setLayoutParams(editParams);
        formLayout.addView(nameEdit);
        
        // 物料类别
        TextView categoryLabel = new TextView(this);
        categoryLabel.setText("物料类别 *");
        categoryLabel.setTextSize(16);
        categoryLabel.setTextColor(0xFF3E2723);
        categoryLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(categoryLabel);
        
        categorySpinner = new Spinner(this);
        categorySpinner.setPadding(16, 16, 16, 16);
        categorySpinner.setBackgroundColor(0xFFF5F5F5);
        categorySpinner.setLayoutParams(editParams);
        formLayout.addView(categorySpinner);
        
        // 当前库存
        TextView currentStockLabel = new TextView(this);
        currentStockLabel.setText("当前库存 *");
        currentStockLabel.setTextSize(16);
        currentStockLabel.setTextColor(0xFF3E2723);
        currentStockLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(currentStockLabel);
        
        currentStockEdit = new EditText(this);
        currentStockEdit.setHint("请输入当前库存数量");
        currentStockEdit.setInputType(android.text.InputType.TYPE_CLASS_NUMBER | android.text.InputType.TYPE_NUMBER_FLAG_DECIMAL);
        currentStockEdit.setPadding(16, 16, 16, 16);
        currentStockEdit.setBackgroundColor(0xFFF5F5F5);
        currentStockEdit.setLayoutParams(editParams);
        formLayout.addView(currentStockEdit);
        
        // 最低库存
        TextView minStockLabel = new TextView(this);
        minStockLabel.setText("最低库存 *");
        minStockLabel.setTextSize(16);
        minStockLabel.setTextColor(0xFF3E2723);
        minStockLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(minStockLabel);
        
        minStockEdit = new EditText(this);
        minStockEdit.setHint("请输入最低库存数量");
        minStockEdit.setInputType(android.text.InputType.TYPE_CLASS_NUMBER | android.text.InputType.TYPE_NUMBER_FLAG_DECIMAL);
        minStockEdit.setPadding(16, 16, 16, 16);
        minStockEdit.setBackgroundColor(0xFFF5F5F5);
        minStockEdit.setLayoutParams(editParams);
        formLayout.addView(minStockEdit);
        
        // 计量单位
        TextView unitLabel = new TextView(this);
        unitLabel.setText("计量单位 *");
        unitLabel.setTextSize(16);
        unitLabel.setTextColor(0xFF3E2723);
        unitLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(unitLabel);
        
        unitSpinner = new Spinner(this);
        unitSpinner.setPadding(16, 16, 16, 16);
        unitSpinner.setBackgroundColor(0xFFF5F5F5);
        unitSpinner.setLayoutParams(editParams);
        formLayout.addView(unitSpinner);
        
        // 单位成本
        TextView unitCostLabel = new TextView(this);
        unitCostLabel.setText("单位成本 (元)");
        unitCostLabel.setTextSize(16);
        unitCostLabel.setTextColor(0xFF3E2723);
        unitCostLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(unitCostLabel);
        
        unitCostEdit = new EditText(this);
        unitCostEdit.setHint("请输入单位成本");
        unitCostEdit.setInputType(android.text.InputType.TYPE_CLASS_NUMBER | android.text.InputType.TYPE_NUMBER_FLAG_DECIMAL);
        unitCostEdit.setPadding(16, 16, 16, 16);
        unitCostEdit.setBackgroundColor(0xFFF5F5F5);
        unitCostEdit.setLayoutParams(editParams);
        formLayout.addView(unitCostEdit);
        
        // 按钮容器
        LinearLayout buttonLayout = new LinearLayout(this);
        buttonLayout.setOrientation(LinearLayout.HORIZONTAL);
        buttonLayout.setPadding(0, 24, 0, 0);
        
        // 取消按钮
        Button cancelButton = new Button(this);
        cancelButton.setText("取消");
        cancelButton.setTextColor(0xFF757575);
        cancelButton.setBackgroundColor(0xFFE0E0E0);
        LinearLayout.LayoutParams buttonParams = new LinearLayout.LayoutParams(0, 120, 1);
        buttonParams.setMargins(0, 0, 8, 0);
        cancelButton.setLayoutParams(buttonParams);
        cancelButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        buttonLayout.addView(cancelButton);
        
        // 保存按钮
        Button saveButton = new Button(this);
        saveButton.setText(isEditMode ? "更新库存" : "保存库存");
        saveButton.setTextColor(0xFFFFFFFF);
        saveButton.setBackgroundColor(0xFFFF5722);
        LinearLayout.LayoutParams saveParams = new LinearLayout.LayoutParams(0, 120, 1);
        saveParams.setMargins(8, 0, 0, 0);
        saveButton.setLayoutParams(saveParams);
        saveButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                saveInventory();
            }
        });
        buttonLayout.addView(saveButton);
        
        formLayout.addView(buttonLayout);
        mainLayout.addView(formLayout);
        scrollView.addView(mainLayout);
        setContentView(scrollView);
    }
    
    private void setupSpinners() {
        // 类别选项
        String[] categories = {"面粉类", "糖类", "乳制品", "水果类", "巧克力类", "坚果类", "香料类", "其他"};
        ArrayAdapter<String> categoryAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, categories);
        categoryAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        categorySpinner.setAdapter(categoryAdapter);
        
        // 单位选项
        String[] units = {"千克", "克", "升", "毫升", "个", "包", "盒", "袋"};
        ArrayAdapter<String> unitAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, units);
        unitAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        unitSpinner.setAdapter(unitAdapter);
    }
    
    private void saveInventory() {
        String name = nameEdit.getText().toString().trim();
        String currentStockStr = currentStockEdit.getText().toString().trim();
        String minStockStr = minStockEdit.getText().toString().trim();
        String unitCostStr = unitCostEdit.getText().toString().trim();
        
        if (name.isEmpty() || currentStockStr.isEmpty() || minStockStr.isEmpty()) {
            Toast.makeText(this, "请填写必填项目", Toast.LENGTH_SHORT).show();
            return;
        }
        
        try {
            double currentStock = Double.parseDouble(currentStockStr);
            double minStock = Double.parseDouble(minStockStr);
            double unitCost = unitCostStr.isEmpty() ? 0.0 : Double.parseDouble(unitCostStr);
            
            String category = categorySpinner.getSelectedItem().toString();
            String unit = unitSpinner.getSelectedItem().toString();
            
            if (isEditMode) {
                // 更新现有库存
                Inventory inventory = new Inventory();
                inventory.setId(inventoryId);
                inventory.setItemName(name);
                inventory.setCategory(category);
                inventory.setCurrentStock(currentStock);
                inventory.setMinimumStock(minStock);
                inventory.setUnit(unit);
                inventory.setUnitCost(unitCost);
                
                repository.updateInventory(inventory);
                Toast.makeText(this, "库存 \"" + name + "\" 更新成功！", Toast.LENGTH_LONG).show();
            } else {
                // 添加新库存
                Inventory inventory = new Inventory(name, category, currentStock, unit);
                inventory.setMinimumStock(minStock);
                inventory.setUnitCost(unitCost);
                repository.insertInventory(inventory);
                Toast.makeText(this, "库存 \"" + name + "\" 添加成功！", Toast.LENGTH_LONG).show();
            }
            
            finish();
            
        } catch (NumberFormatException e) {
            Toast.makeText(this, "请输入正确的数字格式", Toast.LENGTH_SHORT).show();
        }
    }
    
    private void loadInventoryData() {
        inventoryId = getIntent().getLongExtra("INVENTORY_ID", -1);
        String name = getIntent().getStringExtra("INVENTORY_NAME");
        String category = getIntent().getStringExtra("INVENTORY_CATEGORY");
        double currentStock = getIntent().getDoubleExtra("INVENTORY_CURRENT_STOCK", 0.0);
        double minStock = getIntent().getDoubleExtra("INVENTORY_MIN_STOCK", 0.0);
        String unit = getIntent().getStringExtra("INVENTORY_UNIT");
        double unitCost = getIntent().getDoubleExtra("INVENTORY_UNIT_COST", 0.0);
        
        nameEdit.setText(name);
        currentStockEdit.setText(String.valueOf(currentStock));
        minStockEdit.setText(String.valueOf(minStock));
        unitCostEdit.setText(String.valueOf(unitCost));
        
        // 设置Spinner选择项
        setSpinnerSelection(categorySpinner, category);
        setSpinnerSelection(unitSpinner, unit);
    }
    
    private void setSpinnerSelection(Spinner spinner, String value) {
        if (value != null && spinner.getAdapter() != null) {
            for (int i = 0; i < spinner.getAdapter().getCount(); i++) {
                if (spinner.getAdapter().getItem(i).toString().equals(value)) {
                    spinner.setSelection(i);
                    break;
                }
            }
        }
    }
}
