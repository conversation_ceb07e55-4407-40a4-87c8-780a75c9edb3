package com.example.cakeshop;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;
import android.widget.ProgressBar;
import com.example.cakeshop.repository.CakeShopRepository;
import com.example.cakeshop.utils.SampleDataInitializer;
import com.example.cakeshop.utils.SessionManager;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class MainActivity extends Activity {
    private CakeShopRepository repository;
    private SessionManager sessionManager;
    private TextView todayOrdersText;
    private TextView todayRevenueText;
    private TextView totalCustomersText;
    private TextView lowStockText;
    private TextView welcomeText;
    private TextView dateText;
    private ProgressBar progressBar;
    private Handler mainHandler;
    private ExecutorService executor;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 设置登录状态管理器
        sessionManager = new SessionManager(this);
        if (!sessionManager.isLoggedIn()) {
            Intent intent = new Intent(this, LoginActivity.class);
            startActivity(intent);
            finish();
            return;
        }

        setContentView(R.layout.activity_main_complete);

        // 初始化组件
        mainHandler = new Handler(Looper.getMainLooper());
        executor = Executors.newSingleThreadExecutor();
        repository = new CakeShopRepository(getApplication());

        initViews();
        setupClickListeners();

        // 显示加载进度
        progressBar.setVisibility(View.VISIBLE);

        // 在后台线程初始化数据
        initializeDataInBackground();
    }

    private void initViews() {
        progressBar = findViewById(R.id.progress_bar);
        todayOrdersText = findViewById(R.id.today_orders_count);
        todayRevenueText = findViewById(R.id.today_revenue_amount);
        totalCustomersText = findViewById(R.id.total_customers_count);
        lowStockText = findViewById(R.id.low_stock_count);
        welcomeText = findViewById(R.id.welcome_message);
        dateText = findViewById(R.id.current_date);

        // 设置当前日期
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日 EEEE", Locale.CHINESE);
        dateText.setText(dateFormat.format(new Date()));

        // 设置欢迎信息
        setWelcomeMessage();
    }

    private void setWelcomeMessage() {
        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        String greeting;
        if (hour < 6) {
            greeting = "夜深了，注意休息";
        } else if (hour < 12) {
            greeting = "早上好";
        } else if (hour < 18) {
            greeting = "下午好";
        } else {
            greeting = "晚上好";
        }
        String userInfo = sessionManager.getUserDisplayInfo();
        welcomeText.setText(greeting + "，" + userInfo);
    }

    private void setupClickListeners() {
        // 产品管理
        findViewById(R.id.btn_products).setOnClickListener(v -> 
            startActivity(new Intent(this, ProductListActivity.class)));

        // 订单管理
        findViewById(R.id.btn_orders).setOnClickListener(v -> 
            startActivity(new Intent(this, OrderListActivity.class)));

        // 库存管理
        findViewById(R.id.btn_inventory).setOnClickListener(v -> {
            if (sessionManager.canManageInventory()) {
                startActivity(new Intent(this, InventoryListActivity.class));
            } else {
                Toast.makeText(this, "您没有权限访问库存管理", Toast.LENGTH_SHORT).show();
            }
        });

        // 客户管理
        findViewById(R.id.btn_customers).setOnClickListener(v -> 
            startActivity(new Intent(this, CustomerListActivity.class)));

        // 员工管理
        findViewById(R.id.btn_employees).setOnClickListener(v -> {
            if (sessionManager.canAccessEmployeeManagement()) {
                startActivity(new Intent(this, EmployeeListActivity.class));
            } else {
                Toast.makeText(this, "您没有权限访问员工管理", Toast.LENGTH_SHORT).show();
            }
        });

        // 报表功能
        findViewById(R.id.btn_reports).setOnClickListener(v -> {
            if (sessionManager.canAccessReports()) {
                startActivity(new Intent(this, ReportsActivity.class));
            } else {
                Toast.makeText(this, "您没有权限访问报表统计", Toast.LENGTH_SHORT).show();
            }
        });


        }


    private void initializeDataInBackground() {
        executor.execute(() -> {
            try {
                // 直接加载统计数据
                loadStatistics();
            } catch (Exception e) {
                mainHandler.post(() -> {
                    Toast.makeText(MainActivity.this,
                        "加载数据时出现错误: " + e.getMessage(),
                        Toast.LENGTH_LONG).show();
                    progressBar.setVisibility(View.GONE);
                });
            }
        });
    }

    private void loadStatistics() {
        try {
            // 获取今日开始时间
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            long todayStart = calendar.getTimeInMillis();

            calendar.add(Calendar.DAY_OF_MONTH, 1);
            long tomorrowStart = calendar.getTimeInMillis();

            executor.execute(() -> {
                try {
                    // 在后台线程获取数据
                    int todayOrdersCount = repository.getTodayOrdersCountSync();
                    double todayRevenue = repository.getTodayRevenueSync();
                    int totalCustomers = repository.getTotalCustomersCountSync();
                    int lowStockCount = repository.getLowStockCountSync();

                    // 在主线程更新UI
                    runOnUiThread(() -> {
                        updateDashboardNumbers(todayOrdersCount, todayRevenue, totalCustomers, lowStockCount);
                        progressBar.setVisibility(View.GONE);
                    });
                } catch (Exception e) {
                    runOnUiThread(() -> {
                        progressBar.setVisibility(View.GONE);
                        Toast.makeText(MainActivity.this,
                            "加载统计数据时出现错误: " + e.getMessage(),
                            Toast.LENGTH_LONG).show();
                    });
                }
            });
        } catch (Exception e) {
            runOnUiThread(() -> {
                progressBar.setVisibility(View.GONE);
                Toast.makeText(MainActivity.this,
                    "加载统计数据时出现错误: " + e.getMessage(),
                    Toast.LENGTH_LONG).show();
            });
        }
    }

    private void updateDashboardNumbers(int ordersCount, double revenue, int customers, int lowStock) {
        todayOrdersText.setText(String.valueOf(ordersCount));
        todayRevenueText.setText(String.format("¥%.2f", revenue));
        totalCustomersText.setText(String.valueOf(customers));
        lowStockText.setText(String.valueOf(lowStock));
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (executor != null) {
            executor.shutdown();
        }
    }

    @Override
    public void onBackPressed() {
        new android.app.AlertDialog.Builder(this)
            .setTitle("退出登录")
            .setMessage("确定要退出登录吗？")
            .setPositiveButton("确定", (dialog, which) -> {
                sessionManager.logoutUser();
                startActivity(new Intent(this, LoginActivity.class));
                finish();
            })
            .setNegativeButton("取消", null)
            .show();
    }
}