package com.example.cakeshop;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import com.example.cakeshop.model.Inventory;
import com.example.cakeshop.repository.CakeShopRepository;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class InventoryListActivity extends Activity implements androidx.lifecycle.LifecycleOwner {

    private LinearLayout inventoryContainer;
    private CakeShopRepository repository;
    private ExecutorService executor;
    private androidx.lifecycle.LifecycleRegistry lifecycleRegistry;

    @Override
    public androidx.lifecycle.Lifecycle getLifecycle() {
        return lifecycleRegistry;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        lifecycleRegistry = new androidx.lifecycle.LifecycleRegistry(this);

        repository = new CakeShopRepository(getApplication());
        executor = Executors.newSingleThreadExecutor();

        createLayout();
        loadInventory();
    }
    
    private void createLayout() {
        ScrollView scrollView = new ScrollView(this);
        scrollView.setBackgroundColor(0xFFFCE4EC);

        LinearLayout mainLayout = new LinearLayout(this);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.setPadding(32, 32, 32, 32);

        // 标题和添加按钮容器
        LinearLayout headerLayout = new LinearLayout(this);
        headerLayout.setOrientation(LinearLayout.HORIZONTAL);
        headerLayout.setPadding(0, 0, 0, 24);

        // 标题
        TextView titleText = new TextView(this);
        titleText.setText("📦 库存管理");
        titleText.setTextSize(24);
        titleText.setTextColor(0xFFFF5722);
        LinearLayout.LayoutParams titleParams = new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1);
        titleText.setLayoutParams(titleParams);
        headerLayout.addView(titleText);

        // 添加库存按钮
        Button addButton = new Button(this);
        addButton.setText("+ 添加库存");
        addButton.setTextSize(14);
        addButton.setTextColor(0xFFFFFFFF);
        addButton.setBackgroundColor(0xFFFF5722);
        addButton.setPadding(24, 12, 24, 12);
        addButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(InventoryListActivity.this, AddInventoryActivity.class);
                startActivity(intent);
            }
        });
        headerLayout.addView(addButton);

        mainLayout.addView(headerLayout);

        // 库存容器
        inventoryContainer = new LinearLayout(this);
        inventoryContainer.setOrientation(LinearLayout.VERTICAL);
        mainLayout.addView(inventoryContainer);

        scrollView.addView(mainLayout);
        setContentView(scrollView);
    }
    
    private void loadInventory() {
        repository.getAllInventory().observe(this, inventories -> {
            inventoryContainer.removeAllViews();

            if (inventories == null || inventories.isEmpty()) {
                TextView emptyText = new TextView(InventoryListActivity.this);
                emptyText.setText("暂无库存数据\n点击右下角圆形按钮添加库存");
                emptyText.setTextSize(16);
                emptyText.setTextColor(0xFF757575);
                emptyText.setPadding(0, 50, 0, 50);
                emptyText.setGravity(android.view.Gravity.CENTER);
                inventoryContainer.addView(emptyText);
                return;
            }

            for (Inventory inventory : inventories) {
                LinearLayout inventoryCard = createInventoryCard(inventory);
                inventoryContainer.addView(inventoryCard);
            }
        });
    }

    private LinearLayout createInventoryCard(Inventory inventory) {
        LinearLayout card = new LinearLayout(this);
        card.setOrientation(LinearLayout.VERTICAL);
        card.setBackgroundColor(0xFFFFFFFF);
        card.setPadding(24, 24, 24, 24);

        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        );
        params.setMargins(0, 0, 0, 16);
        card.setLayoutParams(params);

        // 物料名称
        TextView nameText = new TextView(this);
        nameText.setText(inventory.getItemName());
        nameText.setTextSize(18);
        nameText.setTextColor(0xFF212121);
        nameText.setTypeface(null, android.graphics.Typeface.BOLD);
        card.addView(nameText);

        // 类别和单位
        TextView categoryText = new TextView(this);
        categoryText.setText("类别: " + inventory.getCategory() + " | 单位: " + inventory.getUnit());
        categoryText.setTextSize(14);
        categoryText.setTextColor(0xFF757575);
        categoryText.setPadding(0, 8, 0, 8);
        card.addView(categoryText);

        // 库存信息
        TextView stockText = new TextView(this);
        boolean isLowStock = inventory.getCurrentStock() <= inventory.getMinimumStock();
        String stockStatus = isLowStock ? "⚠️ 库存不足" : "✅ 库存充足";
        stockText.setText(String.format("当前库存: %.1f %s | 最低库存: %.1f %s\n状态: %s",
            inventory.getCurrentStock(), inventory.getUnit(),
            inventory.getMinimumStock(), inventory.getUnit(),
            stockStatus));
        stockText.setTextSize(12);
        stockText.setTextColor(isLowStock ? 0xFFFF5722 : 0xFF4CAF50);
        stockText.setTypeface(null, android.graphics.Typeface.BOLD);
        stockText.setLineSpacing(4, 1.0f);
        card.addView(stockText);

        // 成本信息
        TextView costText = new TextView(this);
        costText.setText(String.format("单价: ¥%.2f/%s | 总价值: ¥%.2f",
            inventory.getUnitCost(), inventory.getUnit(),
            inventory.getCurrentStock() * inventory.getUnitCost()));
        costText.setTextSize(12);
        costText.setTextColor(0xFF9E9E9E);
        costText.setPadding(0, 8, 0, 0);
        card.addView(costText);

        // 操作按钮容器
        LinearLayout buttonLayout = new LinearLayout(this);
        buttonLayout.setOrientation(LinearLayout.HORIZONTAL);
        buttonLayout.setPadding(0, 16, 0, 0);

        // 编辑按钮
        Button editButton = new Button(this);
        editButton.setText("编辑");
        editButton.setTextSize(12);
        editButton.setTextColor(0xFFFFFFFF);
        editButton.setBackgroundColor(0xFF42A5F5);
        editButton.setPadding(16, 8, 16, 8);
        LinearLayout.LayoutParams editParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        editParams.setMargins(0, 0, 8, 0);
        editButton.setLayoutParams(editParams);
        editButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                editInventory(inventory);
            }
        });
        buttonLayout.addView(editButton);

        // 删除按钮
        Button deleteButton = new Button(this);
        deleteButton.setText("删除");
        deleteButton.setTextSize(12);
        deleteButton.setTextColor(0xFFFFFFFF);
        deleteButton.setBackgroundColor(0xFFFF5722);
        deleteButton.setPadding(16, 8, 16, 8);
        deleteButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                deleteInventory(inventory);
            }
        });
        buttonLayout.addView(deleteButton);

        card.addView(buttonLayout);

        return card;
    }

    private void editInventory(Inventory inventory) {
        Intent intent = new Intent(this, AddInventoryActivity.class);
        intent.putExtra("EDIT_MODE", true);
        intent.putExtra("INVENTORY_ID", inventory.getId());
        intent.putExtra("INVENTORY_NAME", inventory.getItemName());
        intent.putExtra("INVENTORY_CATEGORY", inventory.getCategory());
        intent.putExtra("INVENTORY_CURRENT_STOCK", inventory.getCurrentStock());
        intent.putExtra("INVENTORY_MIN_STOCK", inventory.getMinimumStock());
        intent.putExtra("INVENTORY_UNIT", inventory.getUnit());
        intent.putExtra("INVENTORY_UNIT_COST", inventory.getUnitCost());
        startActivity(intent);
    }

    private void deleteInventory(Inventory inventory) {
        new android.app.AlertDialog.Builder(this)
            .setTitle("删除库存")
            .setMessage("确定要删除库存物料 \"" + inventory.getItemName() + "\" 吗？")
            .setPositiveButton("确定", new android.content.DialogInterface.OnClickListener() {
                @Override
                public void onClick(android.content.DialogInterface dialog, int which) {
                    repository.deleteInventory(inventory);
                    android.widget.Toast.makeText(InventoryListActivity.this,
                        "库存物料 \"" + inventory.getItemName() + "\" 已删除",
                        android.widget.Toast.LENGTH_SHORT).show();
                }
            })
            .setNegativeButton("取消", null)
            .show();
    }
}
