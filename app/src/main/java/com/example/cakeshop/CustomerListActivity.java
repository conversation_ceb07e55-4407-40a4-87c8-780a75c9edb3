package com.example.cakeshop;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import com.example.cakeshop.model.Customer;
import com.example.cakeshop.repository.CakeShopRepository;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class CustomerListActivity extends Activity implements androidx.lifecycle.LifecycleOwner {

    private LinearLayout customerContainer;
    private CakeShopRepository repository;
    private ExecutorService executor;
    private androidx.lifecycle.LifecycleRegistry lifecycleRegistry;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        lifecycleRegistry = new androidx.lifecycle.LifecycleRegistry(this);

        repository = new CakeShopRepository(getApplication());
        executor = Executors.newSingleThreadExecutor();

        createLayout();
        loadCustomers();
    }

    private void createLayout() {
        ScrollView scrollView = new ScrollView(this);
        scrollView.setBackgroundColor(0xFFFFF8E1);

        LinearLayout mainLayout = new LinearLayout(this);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.setPadding(32, 32, 32, 32);

        // 标题和添加按钮容器
        LinearLayout headerLayout = new LinearLayout(this);
        headerLayout.setOrientation(LinearLayout.HORIZONTAL);
        headerLayout.setPadding(0, 0, 0, 24);

        // 标题
        TextView titleText = new TextView(this);
        titleText.setText("👥 客户管理");
        titleText.setTextSize(24);
        titleText.setTextColor(0xFF66BB6A);
        LinearLayout.LayoutParams titleParams = new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1);
        titleText.setLayoutParams(titleParams);
        headerLayout.addView(titleText);

        // 添加客户按钮
        Button addButton = new Button(this);
        addButton.setText("+ 添加客户");
        addButton.setTextSize(14);
        addButton.setTextColor(0xFFFFFFFF);
        addButton.setBackgroundColor(0xFF66BB6A);
        addButton.setPadding(24, 12, 24, 12);
        addButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(CustomerListActivity.this, AddCustomerActivity.class);
                startActivity(intent);
            }
        });
        headerLayout.addView(addButton);

        mainLayout.addView(headerLayout);

        // 客户容器
        customerContainer = new LinearLayout(this);
        customerContainer.setOrientation(LinearLayout.VERTICAL);
        mainLayout.addView(customerContainer);

        scrollView.addView(mainLayout);
        setContentView(scrollView);
    }

    private void loadCustomers() {
        repository.getAllCustomers().observe(this, customers -> {
            customerContainer.removeAllViews();

            if (customers == null || customers.isEmpty()) {
                TextView emptyText = new TextView(CustomerListActivity.this);
                emptyText.setText("暂无客户数据\n点击右上角添加客户");
                emptyText.setTextSize(16);
                emptyText.setTextColor(0xFF757575);
                emptyText.setPadding(0, 50, 0, 50);
                emptyText.setGravity(android.view.Gravity.CENTER);
                customerContainer.addView(emptyText);
                return;
            }

            for (Customer customer : customers) {
                LinearLayout customerCard = createCustomerCard(customer);
                customerContainer.addView(customerCard);
            }
        });
    }

    private List<Customer> createSampleCustomers() {
        List<Customer> customers = new java.util.ArrayList<>();
        customers.add(createSampleCustomer("张小明", "13800138001", "<EMAIL>", "VIP客户", 5, 1200.0, 120));
        customers.add(createSampleCustomer("李小红", "13800138002", "<EMAIL>", "普通客户", 2, 350.0, 35));
        customers.add(createSampleCustomer("王大华", "13800138003", "<EMAIL>", "高级客户", 8, 2500.0, 250));
        customers.add(createSampleCustomer("刘总", "13800138004", "<EMAIL>", "企业客户", 12, 8800.0, 880));
        return customers;
    }

    private Customer createSampleCustomer(String name, String phone, String email,
                                        String type, int orders, double spent, int points) {
        Customer customer = new Customer();
        customer.setName(name);
        customer.setPhone(phone);
        customer.setEmail(email);
        customer.setCustomerType(type);
        customer.setTotalOrders(orders);
        customer.setTotalSpent(spent);
        customer.setLoyaltyPoints(points);
        return customer;
    }

    private LinearLayout createCustomerCard(Customer customer) {
        LinearLayout card = new LinearLayout(this);
        card.setOrientation(LinearLayout.VERTICAL);
        card.setBackgroundColor(0xFFFFFFFF);
        card.setPadding(24, 24, 24, 24);

        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        );
        params.setMargins(0, 0, 0, 16);
        card.setLayoutParams(params);

        // 客户名称和类型
        TextView nameText = new TextView(this);
        nameText.setText(customer.getName() + " (" + customer.getCustomerType() + ")");
        nameText.setTextSize(18);
        nameText.setTextColor(0xFF212121);
        nameText.setTypeface(null, android.graphics.Typeface.BOLD);
        card.addView(nameText);

        // 联系方式
        TextView contactText = new TextView(this);
        contactText.setText("📞 " + customer.getPhone() + "\n📧 " + customer.getEmail());
        contactText.setTextSize(14);
        contactText.setTextColor(0xFF757575);
        contactText.setPadding(0, 8, 0, 8);
        card.addView(contactText);

        // 统计信息
        TextView statsText = new TextView(this);
        statsText.setText(String.format("订单数: %d | 消费总额: ¥%.0f | 积分: %d",
            customer.getTotalOrders(), customer.getTotalSpent(), customer.getLoyaltyPoints()));
        statsText.setTextSize(12);
        statsText.setTextColor(0xFF4CAF50);
        statsText.setTypeface(null, android.graphics.Typeface.BOLD);
        card.addView(statsText);

        // 操作按钮容器
        LinearLayout buttonLayout = new LinearLayout(this);
        buttonLayout.setOrientation(LinearLayout.HORIZONTAL);
        buttonLayout.setPadding(0, 16, 0, 0);

        // 编辑按钮
        Button editButton = new Button(this);
        editButton.setText("编辑");
        editButton.setTextSize(12);
        editButton.setTextColor(0xFFFFFFFF);
        editButton.setBackgroundColor(0xFF42A5F5);
        editButton.setPadding(16, 8, 16, 8);
        LinearLayout.LayoutParams editParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        editParams.setMargins(0, 0, 8, 0);
        editButton.setLayoutParams(editParams);
        editButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                editCustomer(customer);
            }
        });
        buttonLayout.addView(editButton);

        // 删除按钮
        Button deleteButton = new Button(this);
        deleteButton.setText("删除");
        deleteButton.setTextSize(12);
        deleteButton.setTextColor(0xFFFFFFFF);
        deleteButton.setBackgroundColor(0xFFFF5722);
        deleteButton.setPadding(16, 8, 16, 8);
        deleteButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                deleteCustomer(customer);
            }
        });
        buttonLayout.addView(deleteButton);

        card.addView(buttonLayout);

        return card;
    }

    private void editCustomer(Customer customer) {
        Intent intent = new Intent(this, AddCustomerActivity.class);
        intent.putExtra("EDIT_MODE", true);
        intent.putExtra("CUSTOMER_ID", customer.getId());
        intent.putExtra("CUSTOMER_NAME", customer.getName());
        intent.putExtra("CUSTOMER_PHONE", customer.getPhone());
        intent.putExtra("CUSTOMER_EMAIL", customer.getEmail());
        intent.putExtra("CUSTOMER_TYPE", customer.getCustomerType());
        startActivity(intent);
    }

    private void deleteCustomer(Customer customer) {
        new android.app.AlertDialog.Builder(this)
            .setTitle("删除客户")
            .setMessage("确定要删除客户 \"" + customer.getName() + "\" 吗？")
            .setPositiveButton("确定", new android.content.DialogInterface.OnClickListener() {
                @Override
                public void onClick(android.content.DialogInterface dialog, int which) {
                    repository.deleteCustomer(customer);
                    android.widget.Toast.makeText(CustomerListActivity.this,
                        "客户 \"" + customer.getName() + "\" 已删除",
                        android.widget.Toast.LENGTH_SHORT).show();
                }
            })
            .setNegativeButton("取消", null)
            .show();
    }

    @Override
    protected void onResume() {
        super.onResume();
        loadCustomers();
    }

    @Override
    public androidx.lifecycle.Lifecycle getLifecycle() {
        return lifecycleRegistry;
    }
}