package com.example.cakeshop.utils;

import android.util.Log;
import java.util.List;
import java.util.ArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import com.example.cakeshop.model.Product;
import com.example.cakeshop.model.Customer;
import com.example.cakeshop.model.Employee;
import com.example.cakeshop.model.Inventory;
import com.example.cakeshop.model.Order;
import com.example.cakeshop.model.OrderItem;
import com.example.cakeshop.model.User;
import com.example.cakeshop.repository.CakeShopRepository;

public class SampleDataInitializer {
    private static final String TAG = "SampleDataInit";
    private static final Object LOCK = new Object();
    private static boolean isInitialized = false;
    
    public static void initializeData(CakeShopRepository repository) {
        if (isInitialized) {
            Log.d(TAG, "Data already initialized");
            return;
        }

        synchronized (LOCK) {
            if (isInitialized) {
                Log.d(TAG, "Data already initialized (double-check)");
                return;
            }

            ExecutorService executor = Executors.newSingleThreadExecutor();
            executor.execute(() -> {
                try {
                    // 检查是否已有数据
                    Log.d(TAG, "Checking existing data...");
                    List<Product> existingProducts = repository.getAllProductsSync();
                    if (existingProducts != null && !existingProducts.isEmpty()) {
                        Log.d(TAG, "Existing data found, skipping initialization");
                        isInitialized = true;
                        return;
                    }

                    Log.d(TAG, "Starting data initialization...");

                    // 初始化基础数据
                    initializeProducts(repository);
                    Log.d(TAG, "Products initialized");
                    
                    initializeCustomers(repository);
                    Log.d(TAG, "Customers initialized");
                    
                    initializeEmployees(repository);
                    Log.d(TAG, "Employees initialized");
                    
                    initializeUsers(repository);
                    Log.d(TAG, "Users initialized");
                    
                    initializeInventory(repository);
                    Log.d(TAG, "Inventory initialized");

                    // 等待基础数据初始化完成
                    Thread.sleep(1000);

                    // 初始化订单（依赖于其他数据）
                    initializeOrders(repository);
                    Log.d(TAG, "Orders initialized");

                    Log.d(TAG, "Data initialization completed");
                    isInitialized = true;

                } catch (InterruptedException e) {
                    Log.e(TAG, "Data initialization interrupted", e);
                } catch (Exception e) {
                    Log.e(TAG, "Error initializing data", e);
                } finally {
                    executor.shutdown();
                    if (!isInitialized) {
                        Log.w(TAG, "Data initialization failed or incomplete");
                    }
                }
            });
        }
    }
    
    private static void initializeProducts(CakeShopRepository repository) {
        Product[] products = {
            new Product("经典生日蛋糕", "香草海绵蛋糕配奶油霜", 168.0, 
                Product.ProductCategory.BIRTHDAY_CAKE.name(), 
                Product.CakeSize.MEDIUM.name(), 
                Product.CakeFlavor.VANILLA.name(), 120),
            
            new Product("巧克力慕斯蛋糕", "浓郁巧克力慕斯，口感丝滑", 198.0, 
                Product.ProductCategory.CHOCOLATE_CAKE.name(), 
                Product.CakeSize.MEDIUM.name(), 
                Product.CakeFlavor.CHOCOLATE.name(), 150),
            
            new Product("草莓芝士蛋糕", "新鲜草莓配芝士蛋糕", 218.0, 
                Product.ProductCategory.CHEESECAKE.name(), 
                Product.CakeSize.MEDIUM.name(), 
                Product.CakeFlavor.STRAWBERRY.name(), 90),
            
            new Product("迷你纸杯蛋糕", "精致小巧的纸杯蛋糕", 15.0, 
                Product.ProductCategory.CUPCAKE.name(), 
                Product.CakeSize.SMALL.name(), 
                Product.CakeFlavor.VANILLA.name(), 30),
            
            new Product("豪华婚礼蛋糕", "三层豪华婚礼蛋糕", 888.0, 
                Product.ProductCategory.WEDDING_CAKE.name(), 
                Product.CakeSize.EXTRA_LARGE.name(), 
                Product.CakeFlavor.VANILLA.name(), 240),
            
            new Product("抹茶红豆蛋糕", "日式抹茶配红豆", 188.0, 
                Product.ProductCategory.CUSTOM_CAKE.name(), 
                Product.CakeSize.MEDIUM.name(), 
                Product.CakeFlavor.MATCHA.name(), 100)
        };
        
        for (Product product : products) {
            repository.insertProduct(product);
        }
    }
    
    private static void initializeCustomers(CakeShopRepository repository) {
        Customer[] customers = {
            new Customer("张小明", "138****8001", "<EMAIL>", 
                Customer.CustomerType.VIP.name()),
            new Customer("李小红", "138****8002", "<EMAIL>", 
                Customer.CustomerType.REGULAR.name()),
            new Customer("王大华", "138****8003", "<EMAIL>", 
                Customer.CustomerType.PREMIUM.name()),
            new Customer("刘总", "138****8004", "<EMAIL>", 
                Customer.CustomerType.CORPORATE.name())
        };
        
        for (Customer customer : customers) {
            repository.insertCustomer(customer);
        }
    }
    
    private static void initializeEmployees(CakeShopRepository repository) {
        Employee[] employees = {
            new Employee("MGR001", "陈店长", 
                Employee.EmployeePosition.MANAGER.name(),
                Employee.EmployeeDepartment.MANAGEMENT.name(), "139****9001"),
            new Employee("BAK001", "李师傅",
                Employee.EmployeePosition.BAKER.name(),
                Employee.EmployeeDepartment.PRODUCTION.name(), "139****9002"),
            new Employee("DEC001", "小美",
                Employee.EmployeePosition.DECORATOR.name(),
                Employee.EmployeeDepartment.PRODUCTION.name(), "139****9003"),
            new Employee("CSH001", "小王",
                Employee.EmployeePosition.CASHIER.name(),
                Employee.EmployeeDepartment.SALES.name(), "139****9004")
        };
        
        for (Employee employee : employees) {
            repository.insertEmployee(employee);
        }
    }
    
    private static void initializeUsers(CakeShopRepository repository) {
        User[] users = {
            new User("Admin@2023", "Xk9#mP2$vL", "陈店长", User.UserRole.ADMIN),
            new User("Baker@2023", "Ry5$kL8#nQ", "李师傅", User.UserRole.EMPLOYEE),
            new User("Deco@2023", "Jw7#hN4$pM", "小美", User.UserRole.EMPLOYEE),
            new User("Cash@2023", "Bt3$vC6#xK", "小王", User.UserRole.EMPLOYEE)
        };

        for (User user : users) {
            user.setActive(true);  // 确保用户处于激活状态
            repository.insertUser(user);
        }
    }
    
    private static void initializeInventory(CakeShopRepository repository) {
        Inventory[] inventories = {
            new Inventory("高筋面粉", Inventory.InventoryCategory.FLOUR.name(), 50.0, 
                Inventory.InventoryUnit.KG.getDisplayName()),
            new Inventory("细砂糖", Inventory.InventoryCategory.SUGAR.name(), 30.0, 
                Inventory.InventoryUnit.KG.getDisplayName()),
            new Inventory("淡奶油", Inventory.InventoryCategory.DAIRY.name(), 5.0, 
                Inventory.InventoryUnit.L.getDisplayName()),
            new Inventory("新鲜草莓", Inventory.InventoryCategory.FRUITS.name(), 2.0, 
                Inventory.InventoryUnit.KG.getDisplayName()),
            new Inventory("黑巧克力", Inventory.InventoryCategory.CHOCOLATE.name(), 3.0, 
                Inventory.InventoryUnit.KG.getDisplayName())
        };
        
        for (Inventory inventory : inventories) {
            inventory.setMinimumStock(10.0);
            inventory.setUnitCost(25.0);
            repository.insertInventory(inventory);
        }
    }
    
    private static void initializeOrders(CakeShopRepository repository) {
        try {
            // 等待1秒确保其他数据初始化完成
            Thread.sleep(1000);

            List<Product> products = repository.getAllProductsSync();
            if (products == null || products.isEmpty()) {
                Log.e(TAG, "订单初始化失败：无法获取产品数据，请确保产品数据已正确初始化");
                return;
            }

            // 创建示例订单
            String[] orderStatuses = {Order.OrderStatus.PENDING.name(), Order.OrderStatus.IN_PRODUCTION.name(), Order.OrderStatus.READY.name()};
            for (int i = 0; i < 3; i++) {
                try {
                    // 假设客户ID从1开始递增
                    long customerId = i + 1;
                    Order order = createOrder(customerId, orderStatuses[i]);

                    // 为每个订单添加1-2个订单项
                    int numItems = 1 + (int)(Math.random() * 2);
                    List<OrderItem> orderItems = new ArrayList<>();
                    for (int j = 0; j < numItems && j < products.size(); j++) {
                        Product product = products.get(j);
                        OrderItem orderItem = new OrderItem();
                        orderItem.setQuantity(1);
                        orderItem.setProductId(product.getProductId());
                        orderItem.setUnitPrice(product.getPrice());
                        orderItem.setSubtotal(product.getPrice());
                        orderItems.add(orderItem);
                    }

                    // 同步插入订单并获取ID
                    long orderId = repository.insertOrderSync(order);
                    if (orderId <= 0) {
                        Log.e(TAG, "订单创建失败：无法获取有效的订单ID");
                        continue;
                    }
                    order.setId(orderId);

                    // 为订单项设置订单ID并插入
                    for (OrderItem item : orderItems) {
                        item.setOrderId(orderId);
                        repository.insertOrderItem(item);
                    }

                    Log.d(TAG, String.format("已成功创建订单：#%s，客户ID：%d，状态：%s",
                        order.getOrderNumber(), order.getCustomerId(), order.getStatus()));

                } catch (Exception e) {
                    Log.e(TAG, "创建单个订单时发生错误: " + e.getMessage(), e);
                }
            }
            Log.d(TAG, "订单初始化完成");

        } catch (InterruptedException e) {
            Log.e(TAG, "订单初始化中断: " + e.getMessage(), e);
        } catch (Exception e) {
            Log.e(TAG, "订单初始化过程发生严重错误: " + e.getMessage(), e);
        }
    }

    private static Order createOrder(long customerId, String status) {
        Order order = new Order();
        order.setCustomerId(customerId);
        order.setStatus(status);
        order.setPaymentStatus(Order.PaymentStatus.PAID.name());
        order.setPaymentMethod(Order.PaymentMethod.WECHAT.name());
        order.setSpecialInstructions("请准时配送");
        order.setDeliveryAddress("上海市浦东新区张江高科技园区");
        order.setContactPhone("138****8888");
        
        // 生成订单编号：时间戳 + 4位随机数
        String timestamp = String.valueOf(System.currentTimeMillis());
        String randomNum = String.format("%04d", (int)(Math.random() * 10000));
        order.setOrderNumber("ORD" + timestamp + randomNum);
        
        // 设置订单时间
        long currentTime = System.currentTimeMillis();
        order.setOrderDate(currentTime);
        order.setUpdatedAt(currentTime);
        
        // 计算订单金额
        double totalAmount = 168.0 + (Math.random() * 100);
        double discountAmount = totalAmount * 0.1; // 10%优惠
        double actualAmount = totalAmount - discountAmount;
        
        order.setTotalAmount(totalAmount);
        order.setDiscountAmount(discountAmount);
        order.setFinalAmount(actualAmount);
        
        return order;
    }
}