package com.example.cakeshop.utils;

import android.content.Context;
import android.content.SharedPreferences;
import com.example.cakeshop.model.User;

public class SessionManager {
    
    private static final String PREF_NAME = "CakeShopSession";
    private static final String KEY_IS_LOGGED_IN = "isLoggedIn";
    private static final String KEY_USER_ID = "userId";
    private static final String KEY_USERNAME = "username";
    private static final String KEY_FULL_NAME = "fullName";
    private static final String KEY_USER_ROLE = "userRole";
    
    private SharedPreferences pref;
    private SharedPreferences.Editor editor;
    private Context context;
    
    public SessionManager(Context context) {
        this.context = context;
        pref = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        editor = pref.edit();
    }
    
    // 创建登录会话
    public void createLoginSession(User user) {
        editor.putBoolean(KEY_IS_LOGGED_IN, true);
        editor.putLong(KEY_USER_ID, user.getId());
        editor.putString(KEY_USERNAME, user.getUsername());
        editor.putString(KEY_FULL_NAME, user.getFullName());
        editor.putString(KEY_USER_ROLE, user.getRole().name());
        editor.commit();
    }
    
    // 检查是否已登录
    public boolean isLoggedIn() {
        return pref.getBoolean(KEY_IS_LOGGED_IN, false);
    }
    
    // 获取当前用户ID
    public long getCurrentUserId() {
        return pref.getLong(KEY_USER_ID, -1);
    }
    
    // 获取当前用户名
    public String getCurrentUsername() {
        return pref.getString(KEY_USERNAME, "");
    }
    
    // 获取当前用户全名
    public String getCurrentUserFullName() {
        return pref.getString(KEY_FULL_NAME, "");
    }
    
    // 获取当前用户角色
    public User.UserRole getCurrentUserRole() {
        String roleStr = pref.getString(KEY_USER_ROLE, "");
        try {
            return User.UserRole.valueOf(roleStr);
        } catch (IllegalArgumentException e) {
            return User.UserRole.EMPLOYEE; // 默认为员工
        }
    }
    
    // 检查是否为管理员
    public boolean isAdmin() {
        return getCurrentUserRole() == User.UserRole.ADMIN;
    }
    
    // 检查是否为员工
    public boolean isEmployee() {
        return getCurrentUserRole() == User.UserRole.EMPLOYEE;
    }
    
    // 检查权限
    public boolean canAccessEmployeeManagement() {
        return isAdmin();
    }
    
    public boolean canAccessReports() {
        return isAdmin();
    }
    
    public boolean canManageProducts() {
        return true; // 所有用户都可以管理产品
    }
    
    public boolean canManageCustomers() {
        return true; // 所有用户都可以管理客户
    }
    
    public boolean canManageOrders() {
        return true; // 所有用户都可以管理订单
    }
    
    public boolean canManageInventory() {
        return true; // 所有用户都可以管理库存
    }
    
    // 登出
    public void logoutUser() {
        editor.clear();
        editor.commit();
    }
    
    // 获取用户详情（用于显示）
    public String getUserDisplayInfo() {
        String fullName = getCurrentUserFullName();
        String role = getCurrentUserRole().getDisplayName();
        return fullName + " (" + role + ")";
    }
}
