package com.example.cakeshop;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import com.example.cakeshop.model.Employee;
import com.example.cakeshop.repository.CakeShopRepository;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class EmployeeListActivity extends Activity {

    private LinearLayout employeeContainer;
    private CakeShopRepository repository;
    private ExecutorService executor;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        repository = new CakeShopRepository(getApplication());
        executor = Executors.newSingleThreadExecutor();

        createLayout();
        loadEmployees();
    }
    
    private void createLayout() {
        ScrollView scrollView = new ScrollView(this);
        scrollView.setBackgroundColor(0xFFFCE4EC);

        LinearLayout mainLayout = new LinearLayout(this);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.setPadding(32, 32, 32, 32);

        // 标题和添加按钮容器
        LinearLayout headerLayout = new LinearLayout(this);
        headerLayout.setOrientation(LinearLayout.HORIZONTAL);
        headerLayout.setPadding(0, 0, 0, 24);

        // 标题
        TextView titleText = new TextView(this);
        titleText.setText("👨‍💼 员工管理");
        titleText.setTextSize(24);
        titleText.setTextColor(0xFFFF6F00);
        LinearLayout.LayoutParams titleParams = new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1);
        titleText.setLayoutParams(titleParams);
        headerLayout.addView(titleText);

        // 添加员工按钮
        Button addButton = new Button(this);
        addButton.setText("+ 添加员工");
        addButton.setTextSize(14);
        addButton.setTextColor(0xFFFFFFFF);
        addButton.setBackgroundColor(0xFFFF6F00);
        addButton.setPadding(24, 12, 24, 12);
        addButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(EmployeeListActivity.this, AddEmployeeActivity.class);
                startActivity(intent);
            }
        });
        headerLayout.addView(addButton);

        mainLayout.addView(headerLayout);

        // 员工容器
        employeeContainer = new LinearLayout(this);
        employeeContainer.setOrientation(LinearLayout.VERTICAL);
        mainLayout.addView(employeeContainer);

        scrollView.addView(mainLayout);
        setContentView(scrollView);
    }
    
    private void loadEmployees() {
        executor.execute(new Runnable() {
            @Override
            public void run() {
                final List<Employee> employees = createSampleEmployees();

                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        employeeContainer.removeAllViews();

                        if (employees == null || employees.isEmpty()) {
                            TextView emptyText = new TextView(EmployeeListActivity.this);
                            emptyText.setText("暂无员工数据\n点击右上角添加员工");
                            emptyText.setTextSize(16);
                            emptyText.setTextColor(0xFF757575);
                            emptyText.setPadding(0, 50, 0, 50);
                            emptyText.setGravity(android.view.Gravity.CENTER);
                            employeeContainer.addView(emptyText);
                            return;
                        }

                        for (Employee employee : employees) {
                            LinearLayout employeeCard = createEmployeeCard(employee);
                            employeeContainer.addView(employeeCard);
                        }
                    }
                });
            }
        });
    }

    private List<Employee> createSampleEmployees() {
        List<Employee> employees = new java.util.ArrayList<>();
        employees.add(createSampleEmployee("EMP001", "陈店长", "店长", "管理部", "13900139001", "<EMAIL>", 8000.0, "2023-01-15"));
        employees.add(createSampleEmployee("EMP002", "李师傅", "烘焙师", "生产部", "13900139002", "<EMAIL>", 6500.0, "2023-03-20"));
        employees.add(createSampleEmployee("EMP003", "小美", "裱花师", "生产部", "13900139003", "<EMAIL>", 6000.0, "2023-05-10"));
        employees.add(createSampleEmployee("EMP004", "小王", "收银员", "销售部", "13900139004", "<EMAIL>", 4500.0, "2023-07-01"));
        employees.add(createSampleEmployee("EMP005", "小张", "服务员", "销售部", "13900139005", "<EMAIL>", 4000.0, "2023-09-15"));
        employees.add(createSampleEmployee("EMP006", "小刘", "清洁员", "后勤部", "13900139006", "<EMAIL>", 3500.0, "2023-11-01"));
        return employees;
    }

    private Employee createSampleEmployee(String empId, String name, String position, String department, String phone, String email, double salary, String hireDate) {
        Employee employee = new Employee();
        employee.setEmployeeId(empId);
        employee.setName(name);
        employee.setPosition(position);
        employee.setDepartment(department);
        employee.setPhone(phone);
        employee.setEmail(email);
        employee.setSalary(salary);
        employee.setHireDate(hireDate);
        employee.setActive(true);
        employee.setCreatedAt(System.currentTimeMillis());
        employee.setUpdatedAt(System.currentTimeMillis());
        return employee;
    }

    private LinearLayout createEmployeeCard(Employee employee) {
        LinearLayout card = new LinearLayout(this);
        card.setOrientation(LinearLayout.VERTICAL);
        card.setBackgroundColor(0xFFFFFFFF);
        card.setPadding(24, 24, 24, 24);

        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        );
        params.setMargins(0, 0, 0, 16);
        card.setLayoutParams(params);

        // 员工姓名和工号
        TextView nameText = new TextView(this);
        nameText.setText(employee.getName() + " (" + employee.getEmployeeId() + ")");
        nameText.setTextSize(18);
        nameText.setTextColor(0xFF212121);
        nameText.setTypeface(null, android.graphics.Typeface.BOLD);
        card.addView(nameText);

        // 职位和部门
        TextView positionText = new TextView(this);
        positionText.setText("职位: " + employee.getPosition() + " | 部门: " + employee.getDepartment());
        positionText.setTextSize(14);
        positionText.setTextColor(0xFF757575);
        positionText.setPadding(0, 8, 0, 8);
        card.addView(positionText);

        // 联系方式和状态
        TextView contactText = new TextView(this);
        contactText.setText("📞 " + employee.getPhone() + " | 状态: " + (employee.isActive() ? "在职" : "离职"));
        contactText.setTextSize(12);
        contactText.setTextColor(employee.isActive() ? 0xFF4CAF50 : 0xFFFF5722);
        contactText.setTypeface(null, android.graphics.Typeface.BOLD);
        card.addView(contactText);

        // 薪资信息
        if (employee.getSalary() > 0) {
            TextView salaryText = new TextView(this);
            salaryText.setText("💰 薪资: " + String.format("%.0f", employee.getSalary()) + " 元/月");
            salaryText.setTextSize(12);
            salaryText.setTextColor(0xFF2E7D32);
            salaryText.setTypeface(null, android.graphics.Typeface.BOLD);
            salaryText.setPadding(0, 4, 0, 0);
            card.addView(salaryText);
        }

        // 入职日期
        if (employee.getHireDate() != null && !employee.getHireDate().isEmpty()) {
            TextView hireDateText = new TextView(this);
            hireDateText.setText("📅 入职日期: " + employee.getHireDate());
            hireDateText.setTextSize(12);
            hireDateText.setTextColor(0xFF757575);
            hireDateText.setPadding(0, 4, 0, 0);
            card.addView(hireDateText);
        }

        // 操作按钮容器
        LinearLayout buttonLayout = new LinearLayout(this);
        buttonLayout.setOrientation(LinearLayout.HORIZONTAL);
        buttonLayout.setPadding(0, 16, 0, 0);

        // 编辑按钮
        Button editButton = new Button(this);
        editButton.setText("编辑");
        editButton.setTextSize(12);
        editButton.setTextColor(0xFFFFFFFF);
        editButton.setBackgroundColor(0xFF42A5F5);
        editButton.setPadding(16, 8, 16, 8);
        LinearLayout.LayoutParams editParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        editParams.setMargins(0, 0, 8, 0);
        editButton.setLayoutParams(editParams);
        editButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                editEmployee(employee);
            }
        });
        buttonLayout.addView(editButton);

        // 删除按钮
        Button deleteButton = new Button(this);
        deleteButton.setText("删除");
        deleteButton.setTextSize(12);
        deleteButton.setTextColor(0xFFFFFFFF);
        deleteButton.setBackgroundColor(0xFFFF5722);
        deleteButton.setPadding(16, 8, 16, 8);
        deleteButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                deleteEmployee(employee);
            }
        });
        buttonLayout.addView(deleteButton);

        card.addView(buttonLayout);

        return card;
    }

    private void editEmployee(Employee employee) {
        Intent intent = new Intent(this, AddEmployeeActivity.class);
        intent.putExtra("EDIT_MODE", true);
        intent.putExtra("EMPLOYEE_ID", employee.getId());
        intent.putExtra("EMPLOYEE_EMP_ID", employee.getEmployeeId());
        intent.putExtra("EMPLOYEE_NAME", employee.getName());
        intent.putExtra("EMPLOYEE_POSITION", employee.getPosition());
        intent.putExtra("EMPLOYEE_DEPARTMENT", employee.getDepartment());
        intent.putExtra("EMPLOYEE_PHONE", employee.getPhone());
        intent.putExtra("EMPLOYEE_EMAIL", employee.getEmail());
        intent.putExtra("EMPLOYEE_SALARY", employee.getSalary());
        intent.putExtra("EMPLOYEE_HIRE_DATE", employee.getHireDate());
        intent.putExtra("EMPLOYEE_NOTES", employee.getNotes());
        intent.putExtra("EMPLOYEE_ACTIVE", employee.isActive());
        startActivity(intent);
    }

    private void deleteEmployee(Employee employee) {
        new android.app.AlertDialog.Builder(this)
            .setTitle("删除员工")
            .setMessage("确定要删除员工 \"" + employee.getName() + "\" 吗？")
            .setPositiveButton("确定", new android.content.DialogInterface.OnClickListener() {
                @Override
                public void onClick(android.content.DialogInterface dialog, int which) {
                    repository.deleteEmployee(employee);
                    android.widget.Toast.makeText(EmployeeListActivity.this,
                        "员工 \"" + employee.getName() + "\" 已删除",
                        android.widget.Toast.LENGTH_SHORT).show();
                    loadEmployees();
                }
            })
            .setNegativeButton("取消", null)
            .show();
    }

    @Override
    protected void onResume() {
        super.onResume();
        loadEmployees();
    }
}
