package com.example.cakeshop.model;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.ForeignKey;
import androidx.room.Index;
import androidx.room.Ignore;

@Entity(tableName = "cart_java",
        foreignKeys = {
            @ForeignKey(entity = Customer.class,
                       parentColumns = "id",
                       childColumns = "customerId",
                       onDelete = ForeignKey.CASCADE),
            @ForeignKey(entity = Product.class,
                       parentColumns = "product_id",
                       childColumns = "productId",
                       onDelete = ForeignKey.CASCADE)
        },
        indices = {@Index("customerId"), @Index("productId")})
public class Cart {
    @PrimaryKey(autoGenerate = true)
    private long id;
    
    private long customerId;
    private long productId;
    private int quantity;
    private double unitPrice;
    private String customization; // 定制要求
    private long addedAt;
    
    // 构造函数
    public Cart() {
        this.quantity = 1;
        this.unitPrice = 0.0;
        this.customization = "";
        this.addedAt = System.currentTimeMillis();
    }
    
    @Ignore
    public Cart(long customerId, long productId, int quantity, double unitPrice) {
        this();
        this.customerId = customerId;
        this.productId = productId;
        this.quantity = quantity;
        this.unitPrice = unitPrice;
    }
    
    // Getter和Setter方法
    public long getId() { return id; }
    public void setId(long id) { this.id = id; }
    
    public long getCustomerId() { return customerId; }
    public void setCustomerId(long customerId) { this.customerId = customerId; }
    
    public long getProductId() { return productId; }
    public void setProductId(long productId) { this.productId = productId; }
    
    public int getQuantity() { return quantity; }
    public void setQuantity(int quantity) { this.quantity = quantity; }
    
    public double getUnitPrice() { return unitPrice; }
    public void setUnitPrice(double unitPrice) { this.unitPrice = unitPrice; }
    
    public String getCustomization() { return customization; }
    public void setCustomization(String customization) { this.customization = customization; }
    
    public long getAddedAt() { return addedAt; }
    public void setAddedAt(long addedAt) { this.addedAt = addedAt; }
    
    // 计算小计
    public double getSubtotal() {
        return quantity * unitPrice;
    }
    
    // 增加数量
    public void increaseQuantity() {
        this.quantity++;
    }
    
    // 减少数量
    public void decreaseQuantity() {
        if (this.quantity > 1) {
            this.quantity--;
        }
    }
}
