package com.example.cakeshop.model;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.Ignore;
import androidx.room.ColumnInfo;

@Entity(tableName = "products")
public class Product {
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "product_id")
    private long productId;

    @ColumnInfo(name = "name")
    private String name;
    
    @ColumnInfo(name = "description")
    private String description;
    
    @ColumnInfo(name = "price")
    private double price;
    
    @ColumnInfo(name = "category")
    private String category;
    
    @ColumnInfo(name = "size")
    private String size;
    
    @ColumnInfo(name = "flavor")
    private String flavor;
    
    @ColumnInfo(name = "image_url")
    private String imageUrl;
    
    @ColumnInfo(name = "is_available")
    private boolean isAvailable;
    
    @ColumnInfo(name = "preparation_time")
    private int preparationTime;
    
    @ColumnInfo(name = "created_at")
    private long createdAt;
    
    @ColumnInfo(name = "updated_at")
    private long updatedAt;

    // Default constructor for Room
    public Product() {
        this.imageUrl = "";  // Default empty image URL
        this.isAvailable = true;
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
    }

    // Full constructor
    @Ignore
    public Product(String name, String description, double price, String category, 
                  String size, String flavor, String imageUrl, int preparationTime) {
        this();  // Call default constructor for default values
        this.name = name;
        this.description = description;
        this.price = price;
        this.category = category;
        this.size = size;
        this.flavor = flavor;
        this.imageUrl = imageUrl;
        this.preparationTime = preparationTime;
    }

    // Convenience constructor without imageUrl
    @Ignore
    public Product(String name, String description, double price, String category,
                  String size, String flavor, int preparationTime) {
        this(name, description, price, category, size, flavor, "", preparationTime);
    }

    // Getters and Setters
    public long getProductId() { return productId; }
    public void setProductId(long productId) { this.productId = productId; }
    public long getId() { return getProductId(); }
    public void setId(long id) { setProductId(id); }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public double getPrice() { return price; }
    public void setPrice(double price) { this.price = price; }

    public String getCategory() { return category; }
    public void setCategory(String category) { this.category = category; }

    public String getSize() { return size; }
    public void setSize(String size) { this.size = size; }

    public String getFlavor() { return flavor; }
    public void setFlavor(String flavor) { this.flavor = flavor; }

    public String getImageUrl() { return imageUrl; }
    public void setImageUrl(String imageUrl) { this.imageUrl = imageUrl; }

    public boolean isAvailable() { return isAvailable; }
    public void setAvailable(boolean available) { 
        this.isAvailable = available;
        this.updatedAt = System.currentTimeMillis();
    }

    public int getPreparationTime() { return preparationTime; }
    public void setPreparationTime(int preparationTime) { 
        this.preparationTime = preparationTime;
        this.updatedAt = System.currentTimeMillis();
    }

    public long getCreatedAt() { return createdAt; }
    public void setCreatedAt(long createdAt) { this.createdAt = createdAt; }

    public long getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(long updatedAt) { this.updatedAt = updatedAt; }

    // Product category enum
    public enum ProductCategory {
        BIRTHDAY_CAKE("生日蛋糕"),
        WEDDING_CAKE("婚礼蛋糕"),
        CUPCAKE("纸杯蛋糕"),
        CHEESECAKE("芝士蛋糕"),
        FRUIT_CAKE("水果蛋糕"),
        CHOCOLATE_CAKE("巧克力蛋糕"),
        CUSTOM_CAKE("定制蛋糕");

        private final String displayName;

        ProductCategory(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    // Cake size enum
    public enum CakeSize {
        SMALL("小号 (6寸)", 6),
        MEDIUM("中号 (8寸)", 10),
        LARGE("大号 (10寸)", 15),
        EXTRA_LARGE("特大号 (12寸)", 20),
        CUSTOM("定制尺寸", 0);

        private final String displayName;
        private final int servings;

        CakeSize(String displayName, int servings) {
            this.displayName = displayName;
            this.servings = servings;
        }

        public String getDisplayName() {
            return displayName;
        }

        public int getServings() {
            return servings;
        }
    }

    // Cake flavor enum
    public enum CakeFlavor {
        VANILLA("香草"),
        CHOCOLATE("巧克力"),
        STRAWBERRY("草莓"),
        MATCHA("抹茶"),
        TIRAMISU("提拉米苏"),
        RED_VELVET("红丝绒"),
        LEMON("柠檬"),
        COFFEE("咖啡"),
        FRUIT_MIX("混合水果");

        private final String displayName;

        CakeFlavor(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }
}