package com.example.cakeshop.model;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.ForeignKey;
import androidx.room.Index;
import androidx.room.Ignore;

@Entity(tableName = "transactions_java",
        foreignKeys = {
            @ForeignKey(entity = Customer.class,
                       parentColumns = "id",
                       childColumns = "customerId",
                       onDelete = ForeignKey.CASCADE)
        },
        indices = {@Index("customerId")})
public class Transaction {
    @PrimaryKey(autoGenerate = true)
    private long id;
    
    private long customerId;
    private String transactionType; // RECHARGE, PAYMENT, REFUND
    private double amount;
    private double balanceBefore;
    private double balanceAfter;
    private String description;
    private String paymentMethod; // CASH, CARD, ALIPAY, WECHAT, WALLET
    private long orderId; // 关联订单ID，充值时为0
    private long createdAt;
    
    // 构造函数
    public Transaction() {
        this.amount = 0.0;
        this.balanceBefore = 0.0;
        this.balanceAfter = 0.0;
        this.description = "";
        this.paymentMethod = "";
        this.orderId = 0;
        this.createdAt = System.currentTimeMillis();
    }
    
    @Ignore
    public Transaction(long customerId, String transactionType, double amount,
                      double balanceBefore, double balanceAfter, String description) {
        this();
        this.customerId = customerId;
        this.transactionType = transactionType;
        this.amount = amount;
        this.balanceBefore = balanceBefore;
        this.balanceAfter = balanceAfter;
        this.description = description;
    }
    
    // Getter和Setter方法
    public long getId() { return id; }
    public void setId(long id) { this.id = id; }
    
    public long getCustomerId() { return customerId; }
    public void setCustomerId(long customerId) { this.customerId = customerId; }
    
    public String getTransactionType() { return transactionType; }
    public void setTransactionType(String transactionType) { this.transactionType = transactionType; }
    
    public double getAmount() { return amount; }
    public void setAmount(double amount) { this.amount = amount; }
    
    public double getBalanceBefore() { return balanceBefore; }
    public void setBalanceBefore(double balanceBefore) { this.balanceBefore = balanceBefore; }
    
    public double getBalanceAfter() { return balanceAfter; }
    public void setBalanceAfter(double balanceAfter) { this.balanceAfter = balanceAfter; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public String getPaymentMethod() { return paymentMethod; }
    public void setPaymentMethod(String paymentMethod) { this.paymentMethod = paymentMethod; }
    
    public long getOrderId() { return orderId; }
    public void setOrderId(long orderId) { this.orderId = orderId; }
    
    public long getCreatedAt() { return createdAt; }
    public void setCreatedAt(long createdAt) { this.createdAt = createdAt; }
    
    // 交易类型枚举
    public enum TransactionType {
        RECHARGE("充值"),
        PAYMENT("支付"),
        REFUND("退款");
        
        private final String displayName;
        
        TransactionType(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
    
    // 支付方式枚举
    public enum PaymentMethod {
        CASH("现金"),
        CARD("银行卡"),
        ALIPAY("支付宝"),
        WECHAT("微信支付"),
        WALLET("钱包余额");
        
        private final String displayName;
        
        PaymentMethod(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
}
