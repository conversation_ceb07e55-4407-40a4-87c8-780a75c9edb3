package com.example.cakeshop.model;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.Ignore;

@Entity(tableName = "customers_java")
public class Customer {
    @PrimaryKey(autoGenerate = true)
    private long id;

    private String name;
    private String phone;
    private String email;
    private String address;
    private String birthday;
    private String customerType;
    private int totalOrders;
    private double totalSpent;
    private int loyaltyPoints;
    private String notes;
    private long createdAt;
    private long updatedAt;

    // 新增字段：钱包余额和用户等级
    private double walletBalance;
    private String customerLevel;

    // 构造函数
    public Customer() {
        this.email = "";
        this.address = "";
        this.birthday = "";
        this.customerType = CustomerType.REGULAR.name();
        this.totalOrders = 0;
        this.totalSpent = 0.0;
        this.loyaltyPoints = 0;
        this.notes = "";
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
        this.walletBalance = 0.0;
        this.customerLevel = CustomerLevel.BRONZE.name();
    }

    @Ignore
    public Customer(String name, String phone) {
        this();
        this.name = name;
        this.phone = phone;
    }

    @Ignore
    public Customer(String name, String phone, String email, String customerType) {
        this(name, phone);
        this.email = email;
        this.customerType = customerType;
    }

    // Getter和Setter方法
    public long getId() { return id; }
    public void setId(long id) { this.id = id; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    public String getAddress() { return address; }
    public void setAddress(String address) { this.address = address; }

    public String getBirthday() { return birthday; }
    public void setBirthday(String birthday) { this.birthday = birthday; }

    public String getCustomerType() { return customerType; }
    public void setCustomerType(String customerType) { this.customerType = customerType; }

    public int getTotalOrders() { return totalOrders; }
    public void setTotalOrders(int totalOrders) { this.totalOrders = totalOrders; }

    public double getTotalSpent() { return totalSpent; }
    public void setTotalSpent(double totalSpent) { this.totalSpent = totalSpent; }

    public int getLoyaltyPoints() { return loyaltyPoints; }
    public void setLoyaltyPoints(int loyaltyPoints) { this.loyaltyPoints = loyaltyPoints; }

    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }

    public long getCreatedAt() { return createdAt; }
    public void setCreatedAt(long createdAt) { this.createdAt = createdAt; }

    public long getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(long updatedAt) { this.updatedAt = updatedAt; }

    // 新增字段的getter和setter
    public double getWalletBalance() { return walletBalance; }
    public void setWalletBalance(double walletBalance) { this.walletBalance = walletBalance; }

    public String getCustomerLevel() { return customerLevel; }
    public void setCustomerLevel(String customerLevel) { this.customerLevel = customerLevel; }

    // 钱包操作方法
    public void addToWallet(double amount) {
        this.walletBalance += amount;
        this.updatedAt = System.currentTimeMillis();
    }

    public boolean deductFromWallet(double amount) {
        if (this.walletBalance >= amount) {
            this.walletBalance -= amount;
            this.updatedAt = System.currentTimeMillis();
            return true;
        }
        return false;
    }

    // 升级客户等级
    public void upgradeLevel() {
        CustomerLevel currentLevel = CustomerLevel.valueOf(this.customerLevel);
        switch (currentLevel) {
            case BRONZE:
                this.customerLevel = CustomerLevel.SILVER.name();
                break;
            case SILVER:
                this.customerLevel = CustomerLevel.GOLD.name();
                break;
            case GOLD:
                this.customerLevel = CustomerLevel.PLATINUM.name();
                break;
            case PLATINUM:
                // 已经是最高等级
                break;
        }
        this.updatedAt = System.currentTimeMillis();
    }

    // 客户类型枚举
    public enum CustomerType {
        REGULAR("普通客户", 0.0),
        VIP("VIP客户", 0.05),
        PREMIUM("高级客户", 0.10),
        CORPORATE("企业客户", 0.15);

        private final String displayName;
        private final double discountRate;

        CustomerType(String displayName, double discountRate) {
            this.displayName = displayName;
            this.discountRate = discountRate;
        }

        public String getDisplayName() {
            return displayName;
        }

        public double getDiscountRate() {
            return discountRate;
        }
    }

    // 客户等级枚举
    public enum CustomerLevel {
        BRONZE("青铜会员", 0.0, 0),
        SILVER("白银会员", 0.05, 1000),
        GOLD("黄金会员", 0.10, 5000),
        PLATINUM("铂金会员", 0.15, 10000);

        private final String displayName;
        private final double discountRate;
        private final double requiredSpent;

        CustomerLevel(String displayName, double discountRate, double requiredSpent) {
            this.displayName = displayName;
            this.discountRate = discountRate;
            this.requiredSpent = requiredSpent;
        }

        public String getDisplayName() {
            return displayName;
        }

        public double getDiscountRate() {
            return discountRate;
        }

        public double getRequiredSpent() {
            return requiredSpent;
        }

        // 根据消费金额自动计算等级
        public static CustomerLevel calculateLevel(double totalSpent) {
            if (totalSpent >= PLATINUM.requiredSpent) {
                return PLATINUM;
            } else if (totalSpent >= GOLD.requiredSpent) {
                return GOLD;
            } else if (totalSpent >= SILVER.requiredSpent) {
                return SILVER;
            } else {
                return BRONZE;
            }
        }
    }
}