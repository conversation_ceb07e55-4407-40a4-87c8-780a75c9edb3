package com.example.cakeshop.model;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.Ignore;
@Entity(tableName = "users_java")
public class User {
    
    @PrimaryKey(autoGenerate = true)
    private long id;
    
    private String username;
    private String password;
    private String fullName;
    private UserRole role;
    private boolean isActive;
    private long createdAt;
    private long lastLoginAt;
    
    // 用户角色枚举
    public enum UserRole {
        ADMIN("管理员"),
        EMPLOYEE("员工");
        
        private final String displayName;
        
        UserRole(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
    
    // 构造函数
    public User() {
        this.isActive = true;
        this.createdAt = System.currentTimeMillis();
    }
    
    @Ignore
    public User(String username, String password, String fullName, UserRole role) {
        this();
        this.username = username;
        this.password = password;
        this.fullName = fullName;
        this.role = role;
    }
    
    // Getters and Setters
    public long getId() {
        return id;
    }
    
    public void setId(long id) {
        this.id = id;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getFullName() {
        return fullName;
    }
    
    public void setFullName(String fullName) {
        this.fullName = fullName;
    }
    
    public UserRole getRole() {
        return role;
    }
    
    public void setRole(UserRole role) {
        this.role = role;
    }
    
    public boolean isActive() {
        return isActive;
    }
    
    public void setActive(boolean active) {
        isActive = active;
    }
    
    public long getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }
    
    public long getLastLoginAt() {
        return lastLoginAt;
    }
    
    public void setLastLoginAt(long lastLoginAt) {
        this.lastLoginAt = lastLoginAt;
    }
    
    // 权限检查方法
    public boolean isAdmin() {
        return role == UserRole.ADMIN;
    }
    
    public boolean isEmployee() {
        return role == UserRole.EMPLOYEE;
    }
    
    public boolean canAccessEmployeeManagement() {
        return isAdmin();
    }
    
    public boolean canAccessReports() {
        return isAdmin();
    }
    
    public boolean canManageProducts() {
        return true; // 所有用户都可以管理产品
    }
    
    public boolean canManageCustomers() {
        return true; // 所有用户都可以管理客户
    }
    
    public boolean canManageOrders() {
        return true; // 所有用户都可以管理订单
    }
    
    public boolean canManageInventory() {
        return true; // 所有用户都可以管理库存
    }
}