package com.example.cakeshop.model;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.Ignore;

@Entity(tableName = "employees_java")
public class Employee {
    @PrimaryKey(autoGenerate = true)
    private long id;

    private String employeeId;
    private String name;
    private String position;
    private String department;
    private String phone;
    private String email;
    private double salary;
    private String hireDate;
    private boolean isActive;
    private String notes;
    private long createdAt;
    private long updatedAt;

    // 默认构造函数
    public Employee() {
        this.email = "";
        this.employeeId = "";
        this.name = "";
        this.position = "";
        this.department = "";
        this.phone = "";
        this.salary = 0.0;
        this.hireDate = "";
        this.isActive = true;
        this.notes = "";
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
    }

    @Ignore
    public Employee(String employeeId, String name, String position, String department, String phone) {
        this();
        this.employeeId = employeeId;
        this.name = name;
        this.position = position;
        this.department = department;
        this.phone = phone;
    }

    // Getter和Setter方法
    public long getId() { return id; }
    public void setId(long id) { this.id = id; }

    public String getEmployeeId() { return employeeId; }
    public void setEmployeeId(String employeeId) { this.employeeId = employeeId; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getPosition() { return position; }
    public void setPosition(String position) { this.position = position; }

    public String getDepartment() { return department; }
    public void setDepartment(String department) { this.department = department; }

    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    public double getSalary() { return salary; }
    public void setSalary(double salary) { this.salary = salary; }

    public String getHireDate() { return hireDate; }
    public void setHireDate(String hireDate) { this.hireDate = hireDate; }

    public boolean isActive() { return isActive; }
    public void setActive(boolean active) { isActive = active; }

    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }

    public long getCreatedAt() { return createdAt; }
    public void setCreatedAt(long createdAt) { this.createdAt = createdAt; }

    public long getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(long updatedAt) { this.updatedAt = updatedAt; }

    // 员工职位枚举
    public enum EmployeePosition {
        MANAGER("店长"),
        BAKER("烘焙师"),
        DECORATOR("裱花师"),
        CASHIER("收银员"),
        SALES("销售员"),
        ASSISTANT("助理"),
        DELIVERY("配送员");

        private final String displayName;

        EmployeePosition(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    // 员工部门枚举
    public enum EmployeeDepartment {
        MANAGEMENT("管理部"),
        PRODUCTION("生产部"),
        SALES("销售部"),
        DELIVERY("配送部"),
        FINANCE("财务部");

        private final String displayName;

        EmployeeDepartment(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }
}