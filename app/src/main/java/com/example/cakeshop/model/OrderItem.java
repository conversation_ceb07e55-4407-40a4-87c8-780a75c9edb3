package com.example.cakeshop.model;

import androidx.room.Entity;
import androidx.room.ForeignKey;
import androidx.room.Index;
import androidx.room.PrimaryKey;
import androidx.room.ColumnInfo;

@Entity(
    tableName = "order_items",
    foreignKeys = {
        @ForeignKey(
            entity = Order.class,
            parentColumns = "id",
            childColumns = "order_id",
            onDelete = ForeignKey.CASCADE
        ),
        @ForeignKey(
            entity = Product.class,
            parentColumns = "product_id",
            childColumns = "product_id",
            onDelete = ForeignKey.RESTRICT
        )
    },
    indices = {
        @Index(value = "order_id"),
        @Index(value = "product_id")
    }
)
public class OrderItem {
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "id")
    private long id;

    @ColumnInfo(name = "order_id")
    private long orderId;
    
    @ColumnInfo(name = "product_id")
    private long productId;
    
    @ColumnInfo(name = "quantity")
    private int quantity;
    
    @ColumnInfo(name = "unit_price")
    private double unitPrice;
    
    @ColumnInfo(name = "subtotal")
    private double subtotal;
    
    @ColumnInfo(name = "customizations")
    private String customizations;
    
    @ColumnInfo(name = "created_at")
    private long createdAt;

    @ColumnInfo(name = "updated_at") 
    private long updatedAt;
    // Default constructor
    public OrderItem() {
        this.quantity = 1;
        this.unitPrice = 0.0;
        this.subtotal = 0.0;
        this.customizations = "";
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
    }

    @androidx.room.Ignore
    public OrderItem(long orderId, long productId, int quantity, double unitPrice) {
        this();
        this.orderId = orderId;
        this.productId = productId;
        this.quantity = quantity;
        this.unitPrice = unitPrice;
        this.subtotal = quantity * unitPrice;
    }

    @androidx.room.Ignore
    public OrderItem(long orderId, long productId, int quantity, double unitPrice, String customizations) {
        this(orderId, productId, quantity, unitPrice);
        this.customizations = customizations;
    }
    // Getter和Setter方法
    public long getId() { return id; }
    public void setId(long id) { this.id = id; }

    public long getOrderId() { return orderId; }
    public void setOrderId(long orderId) { this.orderId = orderId; }

    public long getProductId() { return productId; }
    public void setProductId(long productId) { this.productId = productId; }

    public int getQuantity() { return quantity; }
    public void setQuantity(int quantity) {
        this.quantity = quantity;
        this.subtotal = quantity * unitPrice;
        this.updatedAt = System.currentTimeMillis();
    }

    public double getUnitPrice() { return unitPrice; }
    public void setUnitPrice(double unitPrice) {
        this.unitPrice = unitPrice; 
        this.subtotal = quantity * unitPrice;
        this.updatedAt = System.currentTimeMillis();
    }

    public double getSubtotal() { return subtotal; }
    public void setSubtotal(double subtotal) { this.subtotal = subtotal; }

    public String getCustomizations() { return customizations; }
    public void setCustomizations(String customizations) {
        this.customizations = customizations;
        this.updatedAt = System.currentTimeMillis();
    }

    public long getCreatedAt() { return createdAt; }
    public void setCreatedAt(long createdAt) { this.createdAt = createdAt; }

    public long getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(long updatedAt) { this.updatedAt = updatedAt; }

    // Helper method to calculate subtotal
    public void calculateSubtotal() {
        this.subtotal = this.quantity * this.unitPrice;
        this.updatedAt = System.currentTimeMillis();
    }
}