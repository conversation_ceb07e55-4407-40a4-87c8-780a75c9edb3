package com.example.cakeshop.model;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.ForeignKey;
import androidx.room.Ignore;
import androidx.room.Index;
import androidx.room.PrimaryKey;

@Entity(
    tableName = "orders",
    foreignKeys = {
        @ForeignKey(
            entity = Customer.class,
            parentColumns = "id",
            childColumns = "customer_id",
            onDelete = ForeignKey.CASCADE
        )
    },
    indices = {
        @Index(value = "customer_id")
    }
)
public class Order {
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "id")
    private long id;

    @ColumnInfo(name = "customer_id")
    private long customerId;

    @ColumnInfo(name = "order_number")
    private String orderNumber;

    @ColumnInfo(name = "order_date")
    private long orderDate;

    @ColumnInfo(name = "delivery_date")
    private long deliveryDate;

    @ColumnInfo(name = "status")
    private String status;

    @ColumnInfo(name = "total_amount")
    private double totalAmount;

    @ColumnInfo(name = "discount_amount")
    private double discountAmount;

    @ColumnInfo(name = "final_amount")
    private double finalAmount;

    @ColumnInfo(name = "payment_status")
    private String paymentStatus;

    @ColumnInfo(name = "payment_method")
    private String paymentMethod;

    @ColumnInfo(name = "special_instructions")
    private String specialInstructions;

    @ColumnInfo(name = "delivery_address")
    private String deliveryAddress;

    @ColumnInfo(name = "contact_phone")
    private String contactPhone;

    @ColumnInfo(name = "created_at")
    private long createdAt;

    @ColumnInfo(name = "updated_at")
    private long updatedAt;

    // 构造函数
    public Order() {
        this.orderDate = System.currentTimeMillis();
        this.status = OrderStatus.PENDING.name();
        this.totalAmount = 0.0;
        this.discountAmount = 0.0;
        this.finalAmount = 0.0;
        this.paymentStatus = PaymentStatus.UNPAID.name();
        this.paymentMethod = "";
        this.specialInstructions = "";
        this.deliveryAddress = "";
        this.contactPhone = "";
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
    }

    @Ignore
    public Order(long customerId, String orderNumber, long deliveryDate) {
        this();
        this.customerId = customerId;
        this.orderNumber = orderNumber;
        this.deliveryDate = deliveryDate;
    }

    // Getter和Setter方法
    public long getId() { return id; }
    public void setId(long id) { this.id = id; }

    public long getCustomerId() { return customerId; }
    public void setCustomerId(long customerId) { this.customerId = customerId; }

    public String getOrderNumber() { return orderNumber; }
    public void setOrderNumber(String orderNumber) { this.orderNumber = orderNumber; }

    public long getOrderDate() { return orderDate; }
    public void setOrderDate(long orderDate) { this.orderDate = orderDate; }

    public long getDeliveryDate() { return deliveryDate; }
    public void setDeliveryDate(long deliveryDate) { this.deliveryDate = deliveryDate; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    public double getTotalAmount() { return totalAmount; }
    public void setTotalAmount(double totalAmount) { this.totalAmount = totalAmount; }

    public double getDiscountAmount() { return discountAmount; }
    public void setDiscountAmount(double discountAmount) { this.discountAmount = discountAmount; }

    public double getFinalAmount() { return finalAmount; }
    public void setFinalAmount(double finalAmount) { this.finalAmount = finalAmount; }

    public String getPaymentStatus() { return paymentStatus; }
    public void setPaymentStatus(String paymentStatus) { this.paymentStatus = paymentStatus; }

    public String getPaymentMethod() { return paymentMethod; }
    public void setPaymentMethod(String paymentMethod) { this.paymentMethod = paymentMethod; }

    public String getSpecialInstructions() { return specialInstructions; }
    public void setSpecialInstructions(String specialInstructions) { this.specialInstructions = specialInstructions; }

    public String getDeliveryAddress() { return deliveryAddress; }
    public void setDeliveryAddress(String deliveryAddress) { this.deliveryAddress = deliveryAddress; }

    public String getContactPhone() { return contactPhone; }
    public void setContactPhone(String contactPhone) { this.contactPhone = contactPhone; }

    public long getCreatedAt() { return createdAt; }
    public void setCreatedAt(long createdAt) { this.createdAt = createdAt; }

    public long getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(long updatedAt) { this.updatedAt = updatedAt; }

    // 订单状态枚举
    public enum OrderStatus {
        PENDING("待确认"),
        CONFIRMED("已确认"),
        IN_PRODUCTION("制作中"),
        READY("待取货"),
        DELIVERED("已交付"),
        CANCELLED("已取消"),
        REFUNDED("已退款");

        private final String displayName;

        OrderStatus(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    // 支付状态枚举
    public enum PaymentStatus {
        UNPAID("未支付"),
        PARTIAL("部分支付"),
        PAID("已支付"),
        REFUNDED("已退款");

        private final String displayName;

        PaymentStatus(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    // 支付方式枚举
    public enum PaymentMethod {
        CASH("现金"),
        CARD("银行卡"),
        WECHAT("微信支付"),
        ALIPAY("支付宝"),
        BANK_TRANSFER("银行转账");

        private final String displayName;

        PaymentMethod(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }
}