package com.example.cakeshop.model;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.Ignore;

@Entity(tableName = "inventory_java")
public class Inventory {
    @PrimaryKey(autoGenerate = true)
    private long id;

    private String itemName;
    private String category;
    private double currentStock;
    private double minimumStock;
    private String unit;
    private double unitCost;
    private String supplier;
    private String location;
    private String expiryDate;
    private String notes;
    private long createdAt;
    private long updatedAt;

    // 构造函数
    public Inventory() {
        this.currentStock = 0.0;
        this.minimumStock = 0.0;
        this.unitCost = 0.0;
        this.supplier = "";
        this.location = "";
        this.expiryDate = "";
        this.notes = "";
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
    }

    @Ignore
    public Inventory(String itemName, String category, double currentStock, String unit) {
        this();
        this.itemName = itemName;
        this.category = category;
        this.currentStock = currentStock;
        this.unit = unit;
    }

    // Getter和Setter方法
    public long getId() { return id; }
    public void setId(long id) { this.id = id; }

    public String getItemName() { return itemName; }
    public void setItemName(String itemName) { this.itemName = itemName; }

    public String getCategory() { return category; }
    public void setCategory(String category) { this.category = category; }

    public double getCurrentStock() { return currentStock; }
    public void setCurrentStock(double currentStock) { this.currentStock = currentStock; }

    public double getMinimumStock() { return minimumStock; }
    public void setMinimumStock(double minimumStock) { this.minimumStock = minimumStock; }

    public String getUnit() { return unit; }
    public void setUnit(String unit) { this.unit = unit; }

    public double getUnitCost() { return unitCost; }
    public void setUnitCost(double unitCost) { this.unitCost = unitCost; }

    public String getSupplier() { return supplier; }
    public void setSupplier(String supplier) { this.supplier = supplier; }

    public String getLocation() { return location; }
    public void setLocation(String location) { this.location = location; }

    public String getExpiryDate() { return expiryDate; }
    public void setExpiryDate(String expiryDate) { this.expiryDate = expiryDate; }

    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }

    public long getCreatedAt() { return createdAt; }
    public void setCreatedAt(long createdAt) { this.createdAt = createdAt; }

    public long getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(long updatedAt) { this.updatedAt = updatedAt; }

    // 辅助方法
    public boolean isLowStock() {
        return currentStock <= minimumStock;
    }

    public double getTotalValue() {
        return currentStock * unitCost;
    }

    // 库存类别枚举
    public enum InventoryCategory {
        FLOUR("面粉类"),
        SUGAR("糖类"),
        DAIRY("乳制品"),
        EGGS("蛋类"),
        FRUITS("水果类"),
        CHOCOLATE("巧克力类"),
        NUTS("坚果类"),
        SPICES("香料类"),
        DECORATIONS("装饰用品"),
        PACKAGING("包装材料"),
        TOOLS("工具设备"),
        OTHER("其他");

        private final String displayName;

        InventoryCategory(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    // 计量单位枚举
    public enum InventoryUnit {
        KG("千克"),
        G("克"),
        L("升"),
        ML("毫升"),
        PIECE("个"),
        PACK("包"),
        BOX("盒"),
        BAG("袋");

        private final String displayName;

        InventoryUnit(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }
}