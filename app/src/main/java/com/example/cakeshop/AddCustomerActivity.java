package com.example.cakeshop;

import android.app.Activity;
import android.os.Bundle;
import android.view.View;
import android.widget.*;
import com.example.cakeshop.model.Customer;
import com.example.cakeshop.repository.CakeShopRepository;

public class AddCustomerActivity extends Activity {

    private EditText nameEdit, phoneEdit, emailEdit;
    private Spinner typeSpinner;
    private CakeShopRepository repository;
    private boolean isEditMode = false;
    private long customerId = -1;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        repository = new CakeShopRepository(getApplication());
        isEditMode = getIntent().getBooleanExtra("EDIT_MODE", false);

        createLayout();
        setupSpinner();

        if (isEditMode) {
            loadCustomerData();
        }
    }

    private void createLayout() {
        ScrollView scrollView = new ScrollView(this);
        scrollView.setBackgroundColor(0xFFFCE4EC);

        LinearLayout mainLayout = new LinearLayout(this);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.setPadding(32, 32, 32, 32);

        // 标题
        TextView titleText = new TextView(this);
        titleText.setText(isEditMode ? "👥 编辑客户" : "👥 添加新客户");
        titleText.setTextSize(24);
        titleText.setTextColor(0xFF66BB6A);
        titleText.setPadding(0, 0, 0, 32);
        mainLayout.addView(titleText);

        // 表单容器
        LinearLayout formLayout = new LinearLayout(this);
        formLayout.setOrientation(LinearLayout.VERTICAL);
        formLayout.setBackgroundColor(0xFFFFFFFF);
        formLayout.setPadding(24, 24, 24, 24);

        LinearLayout.LayoutParams editParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 120);
        editParams.setMargins(0, 8, 0, 16);

        // 客户姓名
        TextView nameLabel = new TextView(this);
        nameLabel.setText("客户姓名 *");
        nameLabel.setTextSize(16);
        nameLabel.setTextColor(0xFF3E2723);
        nameLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(nameLabel);

        nameEdit = new EditText(this);
        nameEdit.setHint("请输入客户姓名");
        nameEdit.setPadding(16, 16, 16, 16);
        nameEdit.setBackgroundColor(0xFFF5F5F5);
        nameEdit.setLayoutParams(editParams);
        formLayout.addView(nameEdit);

        // 联系电话
        TextView phoneLabel = new TextView(this);
        phoneLabel.setText("联系电话 *");
        phoneLabel.setTextSize(16);
        phoneLabel.setTextColor(0xFF3E2723);
        phoneLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(phoneLabel);

        phoneEdit = new EditText(this);
        phoneEdit.setHint("请输入联系电话");
        phoneEdit.setInputType(android.text.InputType.TYPE_CLASS_PHONE);
        phoneEdit.setPadding(16, 16, 16, 16);
        phoneEdit.setBackgroundColor(0xFFF5F5F5);
        phoneEdit.setLayoutParams(editParams);
        formLayout.addView(phoneEdit);

        // 电子邮箱
        TextView emailLabel = new TextView(this);
        emailLabel.setText("电子邮箱");
        emailLabel.setTextSize(16);
        emailLabel.setTextColor(0xFF3E2723);
        emailLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(emailLabel);

        emailEdit = new EditText(this);
        emailEdit.setHint("请输入电子邮箱");
        emailEdit.setInputType(android.text.InputType.TYPE_TEXT_VARIATION_EMAIL_ADDRESS);
        emailEdit.setPadding(16, 16, 16, 16);
        emailEdit.setBackgroundColor(0xFFF5F5F5);
        emailEdit.setLayoutParams(editParams);
        formLayout.addView(emailEdit);

        // 客户类型
        TextView typeLabel = new TextView(this);
        typeLabel.setText("客户类型");
        typeLabel.setTextSize(16);
        typeLabel.setTextColor(0xFF3E2723);
        typeLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(typeLabel);

        typeSpinner = new Spinner(this);
        typeSpinner.setPadding(16, 16, 16, 16);
        typeSpinner.setBackgroundColor(0xFFF5F5F5);
        typeSpinner.setLayoutParams(editParams);
        formLayout.addView(typeSpinner);

        // 按钮容器
        LinearLayout buttonLayout = new LinearLayout(this);
        buttonLayout.setOrientation(LinearLayout.HORIZONTAL);
        buttonLayout.setPadding(0, 24, 0, 0);

        // 取消按钮
        Button cancelButton = new Button(this);
        cancelButton.setText("取消");
        cancelButton.setTextColor(0xFF757575);
        cancelButton.setBackgroundColor(0xFFE0E0E0);
        LinearLayout.LayoutParams buttonParams = new LinearLayout.LayoutParams(0, 120, 1);
        buttonParams.setMargins(0, 0, 8, 0);
        cancelButton.setLayoutParams(buttonParams);
        cancelButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        buttonLayout.addView(cancelButton);

        // 保存按钮
        Button saveButton = new Button(this);
        saveButton.setText(isEditMode ? "更新客户" : "保存客户");
        saveButton.setTextColor(0xFFFFFFFF);
        saveButton.setBackgroundColor(0xFF66BB6A);
        LinearLayout.LayoutParams saveParams = new LinearLayout.LayoutParams(0, 120, 1);
        saveParams.setMargins(8, 0, 0, 0);
        saveButton.setLayoutParams(saveParams);
        saveButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                saveCustomer();
            }
        });
        buttonLayout.addView(saveButton);

        formLayout.addView(buttonLayout);
        mainLayout.addView(formLayout);
        scrollView.addView(mainLayout);
        setContentView(scrollView);
    }

    private void setupSpinner() {
        String[] types = {"普通客户", "VIP客户", "高级客户", "企业客户"};
        ArrayAdapter<String> adapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, types);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        typeSpinner.setAdapter(adapter);
    }

    private void saveCustomer() {
        String name = nameEdit.getText().toString().trim();
        String phone = phoneEdit.getText().toString().trim();
        String email = emailEdit.getText().toString().trim();

        if (name.isEmpty() || phone.isEmpty()) {
            Toast.makeText(this, "请填写必填项目", Toast.LENGTH_SHORT).show();
            return;
        }

        if (phone.length() < 11) {
            Toast.makeText(this, "请输入正确的手机号码", Toast.LENGTH_SHORT).show();
            return;
        }

        String type = typeSpinner.getSelectedItem().toString();

        if (isEditMode) {
            // 更新现有客户
            Customer customer = new Customer();
            customer.setId(customerId);
            customer.setName(name);
            customer.setPhone(phone);
            customer.setEmail(email);
            customer.setCustomerType(type);

            repository.updateCustomer(customer);
            Toast.makeText(this, "客户 \"" + name + "\" 更新成功！", Toast.LENGTH_LONG).show();
        } else {
            // 添加新客户
            Customer customer = new Customer(name, phone, email, type);
            repository.insertCustomer(customer);
            Toast.makeText(this, "客户 \"" + name + "\" 添加成功！", Toast.LENGTH_LONG).show();
        }

        finish();
    }

    private void loadCustomerData() {
        customerId = getIntent().getLongExtra("CUSTOMER_ID", -1);
        String name = getIntent().getStringExtra("CUSTOMER_NAME");
        String phone = getIntent().getStringExtra("CUSTOMER_PHONE");
        String email = getIntent().getStringExtra("CUSTOMER_EMAIL");
        String type = getIntent().getStringExtra("CUSTOMER_TYPE");

        nameEdit.setText(name);
        phoneEdit.setText(phone);
        emailEdit.setText(email);

        // 设置客户类型选择
        if (type != null && typeSpinner.getAdapter() != null) {
            for (int i = 0; i < typeSpinner.getAdapter().getCount(); i++) {
                if (typeSpinner.getAdapter().getItem(i).toString().equals(type)) {
                    typeSpinner.setSelection(i);
                    break;
                }
            }
        }
    }
}
