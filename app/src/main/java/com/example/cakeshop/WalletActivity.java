package com.example.cakeshop;

import android.app.Activity;
import android.os.Bundle;
import android.view.View;
import android.widget.*;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.example.cakeshop.database.CakeShopDatabase;
import com.example.cakeshop.model.Customer;
import com.example.cakeshop.model.Transaction;
import java.util.List;
import java.util.ArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class WalletActivity extends Activity {
    
    private TextView balanceText, customerNameText;
    private EditText rechargeAmountEdit;
    private Spinner paymentMethodSpinner;
    private Button rechargeButton, backButton;
    private RecyclerView transactionRecyclerView;
    private TransactionAdapter transactionAdapter;
    private List<Transaction> transactionList;
    
    private CakeShopDatabase database;
    private ExecutorService executor;
    private long currentCustomerId;
    private Customer currentCustomer;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        database = CakeShopDatabase.getDatabase(this);
        executor = Executors.newSingleThreadExecutor();
        currentCustomerId = getIntent().getLongExtra("customer_id", 1);
        
        createLayout();
        loadCustomerInfo();
        loadTransactionHistory();
    }
    
    private void createLayout() {
        ScrollView scrollView = new ScrollView(this);
        scrollView.setBackgroundColor(0xFFF5F5F5);
        
        LinearLayout mainLayout = new LinearLayout(this);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        
        // 标题栏
        LinearLayout headerLayout = new LinearLayout(this);
        headerLayout.setOrientation(LinearLayout.HORIZONTAL);
        headerLayout.setBackgroundColor(0xFFD81B60);
        headerLayout.setPadding(16, 16, 16, 16);
        
        TextView titleText = new TextView(this);
        titleText.setText("💰 我的钱包");
        titleText.setTextSize(20);
        titleText.setTextColor(0xFFFFFFFF);
        titleText.setTypeface(null, android.graphics.Typeface.BOLD);
        headerLayout.addView(titleText);
        
        mainLayout.addView(headerLayout);
        
        // 钱包余额卡片
        LinearLayout balanceCard = createCard();
        balanceCard.setBackgroundColor(0xFF4CAF50);
        
        customerNameText = new TextView(this);
        customerNameText.setText("客户姓名");
        customerNameText.setTextSize(16);
        customerNameText.setTextColor(0xFFFFFFFF);
        customerNameText.setPadding(0, 0, 0, 8);
        balanceCard.addView(customerNameText);
        
        TextView balanceLabel = new TextView(this);
        balanceLabel.setText("钱包余额");
        balanceLabel.setTextSize(14);
        balanceLabel.setTextColor(0xFFE8F5E8);
        balanceCard.addView(balanceLabel);
        
        balanceText = new TextView(this);
        balanceText.setText("¥0.00");
        balanceText.setTextSize(32);
        balanceText.setTextColor(0xFFFFFFFF);
        balanceText.setTypeface(null, android.graphics.Typeface.BOLD);
        balanceCard.addView(balanceText);
        
        mainLayout.addView(balanceCard);
        
        // 充值卡片
        LinearLayout rechargeCard = createCard();
        
        TextView rechargeTitle = new TextView(this);
        rechargeTitle.setText("💳 钱包充值");
        rechargeTitle.setTextSize(18);
        rechargeTitle.setTextColor(0xFF333333);
        rechargeTitle.setTypeface(null, android.graphics.Typeface.BOLD);
        rechargeTitle.setPadding(0, 0, 0, 16);
        rechargeCard.addView(rechargeTitle);
        
        // 快速充值按钮
        LinearLayout quickRechargeLayout = new LinearLayout(this);
        quickRechargeLayout.setOrientation(LinearLayout.HORIZONTAL);
        
        String[] quickAmounts = {"100", "200", "500", "1000"};
        for (String amount : quickAmounts) {
            Button quickButton = new Button(this);
            quickButton.setText("¥" + amount);
            quickButton.setTextColor(0xFFD81B60);
            quickButton.setBackgroundColor(0xFFF5F5F5);
            LinearLayout.LayoutParams quickParams = new LinearLayout.LayoutParams(
                0, 100, 1.0f);
            quickParams.setMargins(0, 0, 8, 0);
            quickButton.setLayoutParams(quickParams);
            quickButton.setOnClickListener(v -> rechargeAmountEdit.setText(amount));
            quickRechargeLayout.addView(quickButton);
        }
        
        rechargeCard.addView(quickRechargeLayout);
        
        TextView amountLabel = new TextView(this);
        amountLabel.setText("充值金额:");
        amountLabel.setTextSize(16);
        amountLabel.setTextColor(0xFF666666);
        amountLabel.setPadding(0, 16, 0, 8);
        rechargeCard.addView(amountLabel);
        
        rechargeAmountEdit = new EditText(this);
        rechargeAmountEdit.setHint("请输入充值金额");
        rechargeAmountEdit.setInputType(android.text.InputType.TYPE_CLASS_NUMBER | android.text.InputType.TYPE_NUMBER_FLAG_DECIMAL);
        rechargeAmountEdit.setPadding(16, 16, 16, 16);
        rechargeAmountEdit.setBackgroundColor(0xFFF5F5F5);
        LinearLayout.LayoutParams amountParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 120);
        amountParams.setMargins(0, 0, 0, 16);
        rechargeAmountEdit.setLayoutParams(amountParams);
        rechargeCard.addView(rechargeAmountEdit);
        
        TextView paymentLabel = new TextView(this);
        paymentLabel.setText("支付方式:");
        paymentLabel.setTextSize(16);
        paymentLabel.setTextColor(0xFF666666);
        paymentLabel.setPadding(0, 0, 0, 8);
        rechargeCard.addView(paymentLabel);
        
        paymentMethodSpinner = new Spinner(this);
        String[] paymentMethods = {"银行卡支付", "支付宝", "微信支付"};
        ArrayAdapter<String> adapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, paymentMethods);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        paymentMethodSpinner.setAdapter(adapter);
        LinearLayout.LayoutParams spinnerParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 120);
        spinnerParams.setMargins(0, 0, 0, 16);
        paymentMethodSpinner.setLayoutParams(spinnerParams);
        rechargeCard.addView(paymentMethodSpinner);
        
        rechargeButton = new Button(this);
        rechargeButton.setText("立即充值");
        rechargeButton.setTextColor(0xFFFFFFFF);
        rechargeButton.setBackgroundColor(0xFFD81B60);
        LinearLayout.LayoutParams rechargeParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 120);
        rechargeButton.setLayoutParams(rechargeParams);
        rechargeButton.setOnClickListener(v -> performRecharge());
        rechargeCard.addView(rechargeButton);
        
        mainLayout.addView(rechargeCard);
        
        // 交易记录卡片
        LinearLayout historyCard = createCard();
        
        TextView historyTitle = new TextView(this);
        historyTitle.setText("📋 交易记录");
        historyTitle.setTextSize(18);
        historyTitle.setTextColor(0xFF333333);
        historyTitle.setTypeface(null, android.graphics.Typeface.BOLD);
        historyTitle.setPadding(0, 0, 0, 16);
        historyCard.addView(historyTitle);
        
        transactionRecyclerView = new RecyclerView(this);
        transactionRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        LinearLayout.LayoutParams recyclerParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 600);
        transactionRecyclerView.setLayoutParams(recyclerParams);
        historyCard.addView(transactionRecyclerView);
        
        mainLayout.addView(historyCard);
        
        // 返回按钮
        backButton = new Button(this);
        backButton.setText("返回");
        backButton.setTextColor(0xFF666666);
        backButton.setBackgroundColor(0xFFE0E0E0);
        LinearLayout.LayoutParams backParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 120);
        backParams.setMargins(16, 16, 16, 16);
        backButton.setLayoutParams(backParams);
        backButton.setOnClickListener(v -> finish());
        mainLayout.addView(backButton);
        
        scrollView.addView(mainLayout);
        setContentView(scrollView);
    }
    
    private LinearLayout createCard() {
        LinearLayout card = new LinearLayout(this);
        card.setOrientation(LinearLayout.VERTICAL);
        card.setBackgroundColor(0xFFFFFFFF);
        card.setPadding(16, 16, 16, 16);
        LinearLayout.LayoutParams cardParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        cardParams.setMargins(16, 8, 16, 8);
        card.setLayoutParams(cardParams);
        return card;
    }
    
    private void loadCustomerInfo() {
        executor.execute(() -> {
            try {
                // 这里简化处理，实际应该从数据库获取客户信息
                runOnUiThread(() -> {
                    customerNameText.setText("客户姓名"); // 从数据库获取
                    balanceText.setText("¥500.00"); // 从数据库获取钱包余额
                });
            } catch (Exception e) {
                runOnUiThread(() -> {
                    Toast.makeText(this, "加载客户信息失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                });
            }
        });
    }
    
    private void loadTransactionHistory() {
        executor.execute(() -> {
            try {
                List<Transaction> transactions = database.transactionDao().getTransactionsByCustomerSync(currentCustomerId);
                runOnUiThread(() -> {
                    transactionList = transactions;
                    transactionAdapter = new TransactionAdapter(transactionList);
                    transactionRecyclerView.setAdapter(transactionAdapter);
                });
            } catch (Exception e) {
                runOnUiThread(() -> {
                    transactionList = new ArrayList<>();
                    transactionAdapter = new TransactionAdapter(transactionList);
                    transactionRecyclerView.setAdapter(transactionAdapter);
                });
            }
        });
    }
    
    private void performRecharge() {
        String amountStr = rechargeAmountEdit.getText().toString().trim();
        if (amountStr.isEmpty()) {
            Toast.makeText(this, "请输入充值金额", Toast.LENGTH_SHORT).show();
            return;
        }
        
        double amount;
        try {
            amount = Double.parseDouble(amountStr);
            if (amount <= 0) {
                Toast.makeText(this, "充值金额必须大于0", Toast.LENGTH_SHORT).show();
                return;
            }
            if (amount > 10000) {
                Toast.makeText(this, "单次充值金额不能超过10000元", Toast.LENGTH_SHORT).show();
                return;
            }
        } catch (NumberFormatException e) {
            Toast.makeText(this, "请输入有效的金额", Toast.LENGTH_SHORT).show();
            return;
        }
        
        String paymentMethod = paymentMethodSpinner.getSelectedItem().toString();
        
        rechargeButton.setEnabled(false);
        rechargeButton.setText("充值中...");
        
        executor.execute(() -> {
            try {
                // 模拟充值过程
                Thread.sleep(2000);
                
                // 创建交易记录
                Transaction transaction = new Transaction();
                transaction.setCustomerId(currentCustomerId);
                transaction.setTransactionType(Transaction.TransactionType.RECHARGE.name());
                transaction.setAmount(amount);
                transaction.setBalanceBefore(500.0); // 应该从数据库获取当前余额
                transaction.setBalanceAfter(500.0 + amount);
                transaction.setDescription("钱包充值");
                transaction.setPaymentMethod(paymentMethod);
                
                database.transactionDao().insertTransaction(transaction);
                
                // 更新客户钱包余额（这里简化处理）
                
                runOnUiThread(() -> {
                    rechargeButton.setEnabled(true);
                    rechargeButton.setText("立即充值");
                    rechargeAmountEdit.setText("");
                    
                    Toast.makeText(this, "充值成功！", Toast.LENGTH_SHORT).show();
                    
                    // 刷新页面数据
                    loadCustomerInfo();
                    loadTransactionHistory();
                });
                
            } catch (Exception e) {
                runOnUiThread(() -> {
                    rechargeButton.setEnabled(true);
                    rechargeButton.setText("立即充值");
                    Toast.makeText(this, "充值失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                });
            }
        });
    }
    
    // 交易记录适配器
    private class TransactionAdapter extends RecyclerView.Adapter<TransactionAdapter.TransactionViewHolder> {
        private List<Transaction> transactions;
        
        public TransactionAdapter(List<Transaction> transactions) {
            this.transactions = transactions;
        }
        
        @Override
        public TransactionViewHolder onCreateViewHolder(android.view.ViewGroup parent, int viewType) {
            LinearLayout itemLayout = new LinearLayout(WalletActivity.this);
            itemLayout.setOrientation(LinearLayout.VERTICAL);
            itemLayout.setBackgroundColor(0xFFF9F9F9);
            itemLayout.setPadding(16, 12, 16, 12);
            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
            layoutParams.setMargins(0, 0, 0, 4);
            itemLayout.setLayoutParams(layoutParams);
            
            return new TransactionViewHolder(itemLayout);
        }
        
        @Override
        public void onBindViewHolder(TransactionViewHolder holder, int position) {
            Transaction transaction = transactions.get(position);
            holder.bind(transaction);
        }
        
        @Override
        public int getItemCount() {
            return transactions.size();
        }
        
        class TransactionViewHolder extends RecyclerView.ViewHolder {
            private TextView typeText, amountText, timeText, descriptionText;
            
            public TransactionViewHolder(View itemView) {
                super(itemView);
                
                LinearLayout layout = (LinearLayout) itemView;
                
                LinearLayout topLayout = new LinearLayout(WalletActivity.this);
                topLayout.setOrientation(LinearLayout.HORIZONTAL);
                
                typeText = new TextView(WalletActivity.this);
                typeText.setTextSize(16);
                typeText.setTextColor(0xFF333333);
                typeText.setTypeface(null, android.graphics.Typeface.BOLD);
                LinearLayout.LayoutParams typeParams = new LinearLayout.LayoutParams(
                    0, LinearLayout.LayoutParams.WRAP_CONTENT, 1.0f);
                typeText.setLayoutParams(typeParams);
                topLayout.addView(typeText);
                
                amountText = new TextView(WalletActivity.this);
                amountText.setTextSize(16);
                amountText.setTypeface(null, android.graphics.Typeface.BOLD);
                topLayout.addView(amountText);
                
                layout.addView(topLayout);
                
                descriptionText = new TextView(WalletActivity.this);
                descriptionText.setTextSize(14);
                descriptionText.setTextColor(0xFF666666);
                layout.addView(descriptionText);
                
                timeText = new TextView(WalletActivity.this);
                timeText.setTextSize(12);
                timeText.setTextColor(0xFF999999);
                layout.addView(timeText);
            }
            
            public void bind(Transaction transaction) {
                typeText.setText(transaction.getTransactionType());
                
                if ("RECHARGE".equals(transaction.getTransactionType())) {
                    amountText.setText("+¥" + String.format("%.2f", transaction.getAmount()));
                    amountText.setTextColor(0xFF4CAF50);
                } else {
                    amountText.setText("-¥" + String.format("%.2f", transaction.getAmount()));
                    amountText.setTextColor(0xFFFF5722);
                }
                
                descriptionText.setText(transaction.getDescription());
                timeText.setText(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                    .format(new java.util.Date(transaction.getCreatedAt())));
            }
        }
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
}
