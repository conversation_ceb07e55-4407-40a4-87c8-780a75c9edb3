package com.example.cakeshop;

import android.app.Activity;
import android.os.Bundle;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;

public class ReportsActivity extends Activity {
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        createLayout();
    }
    
    private void createLayout() {
        ScrollView scrollView = new ScrollView(this);
        scrollView.setBackgroundColor(0xFFFCE4EC);
        
        LinearLayout mainLayout = new LinearLayout(this);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.setPadding(32, 32, 32, 32);
        
        // 标题
        TextView titleText = new TextView(this);
        titleText.setText("📊 报表统计");
        titleText.setTextSize(24);
        titleText.setTextColor(0xFFD81B60);
        titleText.setPadding(0, 0, 0, 32);
        mainLayout.addView(titleText);
        
        // 销售统计
        LinearLayout salesCard = createReportCard("💰 销售统计", 
            "本月销售额: ¥15,680\n本月订单数: 156\n平均订单金额: ¥100.5\n最佳销售日: 2024-05-25");
        mainLayout.addView(salesCard);
        
        // 产品统计
        LinearLayout productCard = createReportCard("🍰 产品统计", 
            "最受欢迎: 巧克力慕斯蛋糕\n销量最高: 经典生日蛋糕 (45个)\n库存最多: 迷你纸杯蛋糕\n新品推荐: 抹茶红豆蛋糕");
        mainLayout.addView(productCard);
        
        // 客户统计
        LinearLayout customerCard = createReportCard("👥 客户统计", 
            "总客户数: 45人\nVIP客户: 8人\n新增客户: 12人\n活跃客户: 28人");
        mainLayout.addView(customerCard);
        
        // 员工统计
        LinearLayout employeeCard = createReportCard("👨‍💼 员工统计", 
            "在职员工: 4人\n本月出勤率: 98.5%\n最佳员工: 李师傅\n部门分布: 生产部2人, 销售部1人, 管理部1人");
        mainLayout.addView(employeeCard);
        
        // 库存统计
        LinearLayout inventoryCard = createReportCard("📦 库存统计", 
            "库存物料: 5种\n库存充足: 2种\n库存不足: 3种\n需要补货: 淡奶油、新鲜草莓、黑巧克力");
        mainLayout.addView(inventoryCard);
        
        // 趋势分析
        LinearLayout trendCard = createReportCard("📈 趋势分析", 
            "销售趋势: 上升 ↗️\n客户增长: 稳定 ➡️\n库存周转: 良好 ✅\n盈利能力: 优秀 🌟");
        mainLayout.addView(trendCard);
        
        scrollView.addView(mainLayout);
        setContentView(scrollView);
    }
    
    private LinearLayout createReportCard(String title, String content) {
        LinearLayout card = new LinearLayout(this);
        card.setOrientation(LinearLayout.VERTICAL);
        card.setBackgroundColor(0xFFFFFFFF);
        card.setPadding(24, 24, 24, 24);
        
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        );
        params.setMargins(0, 0, 0, 16);
        card.setLayoutParams(params);
        
        // 标题
        TextView titleView = new TextView(this);
        titleView.setText(title);
        titleView.setTextSize(18);
        titleView.setTextColor(0xFF3E2723);
        titleView.setTypeface(null, android.graphics.Typeface.BOLD);
        titleView.setPadding(0, 0, 0, 12);
        card.addView(titleView);
        
        // 内容
        TextView contentView = new TextView(this);
        contentView.setText(content);
        contentView.setTextSize(14);
        contentView.setTextColor(0xFF8D6E63);
        contentView.setLineSpacing(8, 1.0f);
        card.addView(contentView);
        
        return card;
    }
}