package com.example.cakeshop.repository;

import android.app.Application;
import androidx.lifecycle.LiveData;
import com.example.cakeshop.database.*;
import com.example.cakeshop.model.*;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.Calendar;
import android.util.Log;

public class CakeShopRepository {

    private ProductDao productDao;
    private CustomerDao customerDao;
    private OrderDao orderDao;
    private OrderItemDao orderItemDao;
    private EmployeeDao employeeDao;
    private InventoryDao inventoryDao;
    private UserDao userDao;
    private CartDao cartDao;
    private TransactionDao transactionDao;

    private ExecutorService executor;

    public CakeShopRepository(Application application) {
        CakeShopDatabase db = CakeShopDatabase.getDatabase(application);
        productDao = db.productDao();
        customerDao = db.customerDao();
        orderDao = db.orderDao();
        orderItemDao = db.orderItemDao();
        employeeDao = db.employeeDao();
        inventoryDao = db.inventoryDao();
        userDao = db.userDao();
        cartDao = db.cartDao();
        transactionDao = db.transactionDao();

        executor = Executors.newFixedThreadPool(4);
        // 自动初始化演示客户
        initDemoCustomersIfNeeded();
        // 自动初始化演示订单
        initDemoOrdersIfNeeded();
    }

    /**
     * 自动插入演示客户，确保ID为1~4的客户存在
     */
    private void initDemoCustomersIfNeeded() {
        executor.execute(() -> {
            try {
                if (customerDao.getTotalCustomersCountSync() >= 4) return;
                // 插入4个演示客户
                Customer c1 = new Customer("张小明", "13800000001", "<EMAIL>", "REGULAR");
                Customer c2 = new Customer("李小红", "13800000002", "<EMAIL>", "VIP");
                Customer c3 = new Customer("王大华", "13800000003", "<EMAIL>", "REGULAR");
                Customer c4 = new Customer("刘总", "13800000004", "<EMAIL>", "ENTERPRISE");
                c1.setId(1L); c2.setId(2L); c3.setId(3L); c4.setId(4L);
                customerDao.insertCustomer(c1);
                customerDao.insertCustomer(c2);
                customerDao.insertCustomer(c3);
                customerDao.insertCustomer(c4);
            } catch (Exception e) {
                Log.e("CakeShopRepository", "初始化演示客户失败", e);
            }
        });
    }

    /**
     * 自动插入演示订单，确保主页有11条订单可演示（使用真实客户和产品ID）
     */
    private void initDemoOrdersIfNeeded() {
        executor.execute(() -> {
            try {
                Log.d("CakeShopRepository", "[演示订单] 开始检测数据库订单数量...");
                List<Order> existOrders = orderDao.getAllOrders().getValue();
                if (existOrders != null) Log.d("CakeShopRepository", "[演示订单] 当前订单数: " + existOrders.size());
                if (existOrders != null && existOrders.size() > 0) return;
                List<Customer> customers = customerDao.getAllCustomersSync();
                List<Product> products = productDao.getAllProductsSync();
                Log.d("CakeShopRepository", "[演示订单] 客户数: " + (customers == null ? 0 : customers.size()) + ", 产品数: " + (products == null ? 0 : products.size()));
                if (customers == null || customers.size() == 0 || products == null || products.size() < 3) return;
                int orderCount = 11;
                for (int i = 0; i < orderCount; i++) {
                    Customer customer = customers.get(i % customers.size());
                    Product product = products.get(i % 3);
                    Order order = new Order();
                    order.setCustomerId(customer.getId());
                    order.setOrderNumber("ORD2025" + (1000 + i));
                    order.setOrderDate(System.currentTimeMillis() - (orderCount - i) * 86400000L);
                    order.setStatus(Order.OrderStatus.CONFIRMED.name());
                    order.setTotalAmount(product.getPrice() * (i % 5 + 1));
                    order.setDiscountAmount(0.0);
                    order.setFinalAmount(order.getTotalAmount());
                    order.setPaymentStatus(Order.PaymentStatus.UNPAID.name());
                    order.setPaymentMethod(Order.PaymentMethod.CASH.name());
                    order.setSpecialInstructions("");
                    order.setDeliveryAddress("演示地址" + (i + 1));
                    order.setContactPhone(customer.getPhone());
                    order.setCreatedAt(order.getOrderDate());
                    order.setUpdatedAt(order.getOrderDate());
                    long orderId = orderDao.insertOrder(order);
                    Log.d("CakeShopRepository", "[演示订单] 插入订单: orderId=" + orderId + ", customerId=" + customer.getId() + ", productId=" + product.getProductId());
                    OrderItem item = new OrderItem();
                    item.setOrderId(orderId);
                    item.setProductId(product.getProductId());
                    item.setQuantity(i % 5 + 1);
                    item.setUnitPrice(product.getPrice());
                    item.setSubtotal(product.getPrice() * (i % 5 + 1));
                    item.setCustomizations("");
                    item.setCreatedAt(order.getOrderDate());
                    item.setUpdatedAt(order.getOrderDate());
                    long itemId = orderItemDao.insertOrderItem(item);
                    Log.d("CakeShopRepository", "[演示订单] 插入订单项: itemId=" + itemId);
                }
                // 插入后再查一次订单数
                List<Order> afterOrders = orderDao.getAllOrders().getValue();
                Log.d("CakeShopRepository", "[演示订单] 插入后订单数: " + (afterOrders == null ? "null" : afterOrders.size()));
            } catch (Exception e) {
                Log.e("CakeShopRepository", "初始化演示订单失败", e);
            }
        });
    }

    // Product operations
    public LiveData<List<Product>> getAllProducts() {
        return productDao.getAllProducts();
    }

    /**
     * 同步获取所有产品
     * 注意：此方法应该只在后台线程中调用
     */
    public List<Product> getAllProductsSync() {
        return productDao.getAllProductsSync();
    }

    private void deleteAllProductsSync() {
        productDao.deleteAllProducts();
    }

    public void resetProducts() {
        executor.execute(() -> {
            try {
                Log.d("CakeShopRepository", "开始重置产品数据");

                // 同步删除所有产品
                Log.d("CakeShopRepository", "删除前的产品数量: " + productDao.getProductCount());
                productDao.deleteAllProducts();
                Log.d("CakeShopRepository", "删除后的产品数量: " + productDao.getProductCount());

                // 确认删除成功
                if (productDao.getProductCount() > 0) {
                    Log.e("CakeShopRepository", "产品未能完全删除");
                    return;
                }

                // 初始化新产品
                Product[] products = {
                    new Product("经典生日蛋糕", "香草海绵蛋糕配奶油霜", 168.0,
                        Product.ProductCategory.BIRTHDAY_CAKE.name(),
                        Product.CakeSize.MEDIUM.name(),
                        Product.CakeFlavor.VANILLA.name(), 120),

                    new Product("巧克力慕斯蛋糕", "浓郁巧克力慕斯，口感丝滑", 198.0,
                        Product.ProductCategory.CHOCOLATE_CAKE.name(),
                        Product.CakeSize.MEDIUM.name(),
                        Product.CakeFlavor.CHOCOLATE.name(), 150),

                    new Product("草莓芝士蛋糕", "新鲜草莓配芝士蛋糕", 218.0,
                        Product.ProductCategory.CHEESECAKE.name(),
                        Product.CakeSize.MEDIUM.name(),
                        Product.CakeFlavor.STRAWBERRY.name(), 90),

                    new Product("迷你纸杯蛋糕", "精致小巧的纸杯蛋糕", 15.0,
                        Product.ProductCategory.CUPCAKE.name(),
                        Product.CakeSize.SMALL.name(),
                        Product.CakeFlavor.VANILLA.name(), 30),

                    new Product("豪华婚礼蛋糕", "三层豪华婚礼蛋糕", 888.0,
                        Product.ProductCategory.WEDDING_CAKE.name(),
                        Product.CakeSize.EXTRA_LARGE.name(),
                        Product.CakeFlavor.VANILLA.name(), 240),

                    new Product("抹茶红豆蛋糕", "日式抹茶配红豆", 188.0,
                        Product.ProductCategory.FRUIT_CAKE.name(),
                        Product.CakeSize.MEDIUM.name(),
                        Product.CakeFlavor.MATCHA.name(), 100)
                };

                // 同步插入所有产品
                int insertedCount = 0;
                for (Product product : products) {
                    try {
                        product.setAvailable(true);
                        productDao.insertProduct(product);
                        insertedCount++;
                        Log.d("CakeShopRepository", String.format("产品 #%d 已插入: %s",
                            insertedCount, product.getName()));
                    } catch (Exception e) {
                        Log.e("CakeShopRepository", "插入产品失败: " + product.getName(), e);
                    }
                }

                // 最终验证
                int finalCount = productDao.getProductCount();
                Log.d("CakeShopRepository", String.format("重置完成。目标数量：%d，实际插入：%d，最终数量：%d", 
                    products.length, insertedCount, finalCount));

            } catch (Exception e) {
                Log.e("CakeShopRepository", "重置产品数据时出错", e);
            }
        });
    }

    public LiveData<Product> getProductById(long id) {
        return productDao.getProductById(id);
    }

    public LiveData<List<Product>> getProductsByCategory(String category) {
        return productDao.getProductsByCategory(category);
    }

    public LiveData<List<Product>> getAvailableProducts() {
        return productDao.getAvailableProducts();
    }

    public LiveData<List<Product>> searchProducts(String searchQuery) {
        return productDao.searchProducts(searchQuery);
    }

    public void insertProduct(Product product) {
        executor.execute(() -> productDao.insertProduct(product));
    }

    public void deleteAllProducts() {
        executor.execute(() -> {
            try {
                Log.d("CakeShopRepository", "正在删除所有产品...");
                productDao.deleteAllProducts();
                Log.d("CakeShopRepository", "所有产品删除完成");
            } catch (Exception e) {
                Log.e("CakeShopRepository", "删除产品时出错: " + e.getMessage());
            }
        });
    }

    public void initializeProducts() {
        executor.execute(() -> {
            try {
                Log.d("CakeShopRepository", "开始初始化产品数据");

                // 检查现有产品
                List<Product> existingProducts = productDao.getAllProductsSync();
                Log.d("CakeShopRepository", "当前产品数量: " + (existingProducts != null ? existingProducts.size() : 0));

                // 初始化示例产品数据
                Product[] products = {
                    new Product("经典生日蛋糕", "香草海绵蛋糕配奶油霜", 168.0,
                        Product.ProductCategory.BIRTHDAY_CAKE.name(),
                        Product.CakeSize.MEDIUM.name(),
                        Product.CakeFlavor.VANILLA.name(), 120),

                    new Product("巧克力慕斯蛋糕", "浓郁巧克力慕斯，口感丝滑", 198.0,
                        Product.ProductCategory.CHOCOLATE_CAKE.name(),
                        Product.CakeSize.MEDIUM.name(),
                        Product.CakeFlavor.CHOCOLATE.name(), 150),

                    new Product("草莓芝士蛋糕", "新鲜草莓配芝士蛋糕", 218.0,
                        Product.ProductCategory.CHEESECAKE.name(),
                        Product.CakeSize.MEDIUM.name(),
                        Product.CakeFlavor.STRAWBERRY.name(), 90),

                    new Product("迷你纸杯蛋糕", "精致小巧的纸杯蛋糕", 15.0,
                        Product.ProductCategory.CUPCAKE.name(),
                        Product.CakeSize.SMALL.name(),
                        Product.CakeFlavor.VANILLA.name(), 30),

                    new Product("豪华婚礼蛋糕", "三层豪华婚礼蛋糕", 888.0,
                        Product.ProductCategory.WEDDING_CAKE.name(),
                        Product.CakeSize.EXTRA_LARGE.name(),
                        Product.CakeFlavor.VANILLA.name(), 240),

                    new Product("抹茶红豆蛋糕", "日式抹茶配红豆", 188.0,
                        Product.ProductCategory.FRUIT_CAKE.name(),
                        Product.CakeSize.MEDIUM.name(),
                        Product.CakeFlavor.MATCHA.name(), 100)
                };

                // 插入所有产品
                Log.d("CakeShopRepository", "开始插入示例产品...");
                for (Product product : products) {
                    try {
                        product.setAvailable(true);
                        productDao.insertProduct(product);
                        Log.d("CakeShopRepository", "成功插入产品: " + product.getName() + 
                            ", 类别: " + product.getCategory() + 
                            ", 尺寸: " + product.getSize() + 
                            ", 口味: " + product.getFlavor());
                    } catch (Exception e) {
                        Log.e("CakeShopRepository", "插入产品失败: " + product.getName() + ", 错误: " + e.getMessage());
                    }
                }

                // 验证插入结果
                List<Product> finalProducts = productDao.getAllProductsSync();
                Log.d("CakeShopRepository", "初始化完成，最终产品数量: " + (finalProducts != null ? finalProducts.size() : 0));

            } catch (Exception e) {
                Log.e("CakeShopRepository", "初始化产品数据时出错: " + e.getMessage());
            }
        });
    }

    public void updateProduct(Product product) {
        executor.execute(() -> productDao.updateProduct(product));
    }

    public interface DeleteProductCallback {
        void onSuccess();
        void onError(String message);
    }

    public void deleteProduct(Product product, DeleteProductCallback callback) {
        executor.execute(() -> {
            try {
                Log.d("CakeShopRepository", "正在删除产品: " + product.getName() + ", ID: " + product.getProductId());

                // 检查是否有订单项引用此产品
                int orderItemCount = orderItemDao.getOrderItemCountByProductId(product.getProductId());
                if (orderItemCount > 0) {
                    Log.w("CakeShopRepository", "产品被 " + orderItemCount + " 个订单项引用，无法删除");
                    if (callback != null) {
                        callback.onError("该产品已被 " + orderItemCount + " 个订单使用，无法删除。请先处理相关订单。");
                    }
                    return;
                }

                productDao.deleteProductImpl(product);
                Log.d("CakeShopRepository", "产品删除成功: " + product.getName());
                if (callback != null) {
                    callback.onSuccess();
                }
            } catch (Exception e) {
                Log.e("CakeShopRepository", "删除产品失败: " + product.getName(), e);
                if (callback != null) {
                    callback.onError("删除失败: " + e.getMessage());
                }
            }
        });
    }

    // Customer operations
    public LiveData<List<Customer>> getAllCustomers() {
        return customerDao.getAllCustomers();
    }

    public List<String> getAllCustomerNames() {
        return customerDao.getAllCustomerNames();
    }

    public LiveData<Customer> getCustomerById(long id) {
        return customerDao.getCustomerById(id);
    }

    public LiveData<List<Customer>> getCustomersByType(String customerType) {
        return customerDao.getCustomersByType(customerType);
    }

    public LiveData<List<Customer>> searchCustomers(String searchQuery) {
        return customerDao.searchCustomers(searchQuery);
    }

    public void insertCustomer(Customer customer) {
        executor.execute(() -> customerDao.insertCustomer(customer));
    }

    public void updateCustomer(Customer customer) {
        executor.execute(() -> customerDao.updateCustomer(customer));
    }

    public void deleteCustomer(Customer customer) {
        executor.execute(() -> customerDao.deleteCustomer(customer));
    }

    public void updateCustomerStats(long customerId, int totalOrders, double totalSpent) {
        executor.execute(() -> customerDao.updateCustomerStats(customerId, totalOrders, totalSpent));
    }

    // Order operations
    public LiveData<List<Order>> getAllOrders() {
        return orderDao.getAllOrders();
    }

    public LiveData<Order> getOrderById(long id) {
        return orderDao.getOrderById(id);
    }

    // 同步获取订单
    public Order getOrderByIdSync(long orderId) {
        return orderDao.getOrderByIdSync(orderId);
    }

    public LiveData<List<Order>> getOrdersByCustomer(long customerId) {
        return orderDao.getOrdersByCustomer(customerId);
    }

    public LiveData<List<Order>> getOrdersByStatus(String status) {
        return orderDao.getOrdersByStatus(status);
    }

    public LiveData<List<Order>> getOrdersByDateRange(long startDate, long endDate) {
        return orderDao.getOrdersByDateRange(startDate, endDate);
    }

    public LiveData<List<Order>> searchOrders(String searchQuery) {
        return orderDao.searchOrders(searchQuery);
    }

    public void insertOrder(Order order) {
        executor.execute(() -> {
            long orderId = orderDao.insertOrder(order);
            order.setId(orderId);
        });
    }

    /**
     * 同步插入订单并返回订单ID
     * 注意：此方法应该只在后台线程中调用
     */
    public long insertOrderSync(Order order) {
        return orderDao.insertOrder(order);
    }

    public void updateOrder(Order order) {
        executor.execute(() -> orderDao.updateOrder(order));
    }

    public void deleteOrder(Order order) {
        executor.execute(() -> orderDao.deleteOrder(order));
    }

    public void updateOrderStatus(long orderId, String status) {
        executor.execute(() -> orderDao.updateOrderStatus(orderId, status));
    }

    // OrderItem operations
    public LiveData<List<OrderItem>> getOrderItems(long orderId) {
        return orderItemDao.getOrderItems(orderId);
    }

    /**
     * 同步获取指定订单的所有订单项
     * 注意：此方法应该只在后台线程中调用
     */
    public List<OrderItem> getOrderItemsByOrderIdSync(long orderId) {
        return orderItemDao.getOrderItemsByOrderIdSync(orderId);
    }

    public LiveData<List<OrderItem>> getOrderItemsByProduct(long productId) {
        return orderItemDao.getOrderItemsByProduct(productId);
    }

    public void insertOrderItem(OrderItem orderItem) {
        executor.execute(() -> orderItemDao.insertOrderItem(orderItem));
    }

    public void insertOrderItems(List<OrderItem> orderItems) {
        executor.execute(() -> orderItemDao.insertOrderItems(orderItems));
    }

    public void updateOrderItem(OrderItem orderItem) {
        executor.execute(() -> orderItemDao.updateOrderItem(orderItem));
    }

    public void deleteOrderItem(OrderItem orderItem) {
        executor.execute(() -> orderItemDao.deleteOrderItem(orderItem));
    }

    public void deleteOrderItemsByOrderId(long orderId) {
        executor.execute(() -> orderItemDao.deleteOrderItemsByOrderId(orderId));
    }

    // Employee operations
    public LiveData<List<Employee>> getAllEmployees() {
        return employeeDao.getAllEmployees();
    }

    public LiveData<Employee> getEmployeeById(long id) {
        return employeeDao.getEmployeeById(id);
    }

    public void insertEmployee(Employee employee) {
        executor.execute(() -> employeeDao.insertEmployee(employee));
    }

    public void updateEmployee(Employee employee) {
        executor.execute(() -> employeeDao.updateEmployee(employee));
    }

    public void deleteEmployee(Employee employee) {
        executor.execute(() -> employeeDao.deleteEmployee(employee));
    }

    // Inventory operations
    public LiveData<List<Inventory>> getAllInventory() {
        return inventoryDao.getAllInventory();
    }

    public LiveData<Inventory> getInventoryById(long id) {
        return inventoryDao.getInventoryById(id);
    }

    public void insertInventory(Inventory inventory) {
        executor.execute(() -> inventoryDao.insertInventory(inventory));
    }

    public void updateInventory(Inventory inventory) {
        executor.execute(() -> inventoryDao.updateInventory(inventory));
    }

    public void deleteInventory(Inventory inventory) {
        executor.execute(() -> inventoryDao.deleteInventory(inventory));
    }

    // Statistics operations
    public LiveData<Integer> getProductCount() {
        return productDao.getProductCountLiveData();
    }

    public LiveData<Integer> getCustomerCount() {
        return customerDao.getCustomerCount();
    }

    public LiveData<Integer> getOrderCount() {
        return orderDao.getOrderCount();
    }

    public int getTodayOrdersCountSync() {
        // 获取今日开始时间
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        long todayStart = calendar.getTimeInMillis();

        calendar.add(Calendar.DAY_OF_MONTH, 1);
        long tomorrowStart = calendar.getTimeInMillis();

        return orderDao.getTodayOrdersCountSync(todayStart, tomorrowStart);
    }

    public double getTodayRevenueSync() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        long todayStart = calendar.getTimeInMillis();

        calendar.add(Calendar.DAY_OF_MONTH, 1);
        long tomorrowStart = calendar.getTimeInMillis();

        return orderDao.getTodayRevenueSync(todayStart, tomorrowStart);
    }

    public int getTotalCustomersCountSync() {
        return customerDao.getTotalCustomersCountSync();
    }

    public int getLowStockCountSync() {
        // Use inventory DAO since products don't have stock fields
        return inventoryDao.getLowStockCountSync();
    }
    // User operations
    public LiveData<List<User>> getAllUsers() {
        return userDao.getAllUsers();
    }

    public LiveData<User> getUserById(long id) {
        return userDao.getUserById(id);
    }

    public User getUserByUsername(String username) {
        return userDao.getUserByUsername(username);
    }

    public User authenticateUser(String username, String password) {
        return userDao.authenticateUser(username, password);
    }

    public void insertUser(User user) {
        executor.execute(() -> userDao.insertUser(user));
    }

    public void updateUser(User user) {
        executor.execute(() -> userDao.updateUser(user));
    }

    public void deleteUser(User user) {
        executor.execute(() -> userDao.deleteUser(user));
    }

    public void deactivateUser(long id) {
        executor.execute(() -> userDao.deactivateUser(id));
    }

    public void updateLastLogin(long id, long loginTime) {
        executor.execute(() -> userDao.updateLastLogin(id, loginTime));
    }

    public void updatePassword(long id, String newPassword) {
        executor.execute(() -> userDao.updatePassword(id, newPassword));
    }

    // Cart operations
    public LiveData<List<Cart>> getCartByCustomer(long customerId) {
        return cartDao.getCartByCustomer(customerId);
    }

    public List<Cart> getCartByCustomerSync(long customerId) {
        return cartDao.getCartByCustomerSync(customerId);
    }

    public LiveData<Cart> getCartItemById(long id) {
        return cartDao.getCartItemById(id);
    }

    public Cart getCartItemByCustomerAndProduct(long customerId, long productId) {
        return cartDao.getCartItemByCustomerAndProduct(customerId, productId);
    }

    public LiveData<Integer> getCartItemCount(long customerId) {
        return cartDao.getCartItemCount(customerId);
    }

    public int getCartItemCountSync(long customerId) {
        return cartDao.getCartItemCountSync(customerId);
    }

    public LiveData<Double> getCartTotal(long customerId) {
        return cartDao.getCartTotal(customerId);
    }

    public double getCartTotalSync(long customerId) {
        return cartDao.getCartTotalSync(customerId);
    }

    public void insertCartItem(Cart cart) {
        executor.execute(() -> cartDao.insertCartItem(cart));
    }

    public void insertCartItems(List<Cart> carts) {
        executor.execute(() -> cartDao.insertCartItems(carts));
    }

    public void updateCartItem(Cart cart) {
        executor.execute(() -> cartDao.updateCartItem(cart));
    }

    public void updateCartItemQuantity(long id, int quantity) {
        executor.execute(() -> cartDao.updateCartItemQuantity(id, quantity));
    }

    public void increaseCartItemQuantity(long id) {
        executor.execute(() -> cartDao.increaseCartItemQuantity(id));
    }

    public void decreaseCartItemQuantity(long id) {
        executor.execute(() -> cartDao.decreaseCartItemQuantity(id));
    }

    public void deleteCartItem(Cart cart) {
        executor.execute(() -> cartDao.deleteCartItem(cart));
    }

    public void deleteCartItemById(long id) {
        executor.execute(() -> cartDao.deleteCartItemById(id));
    }

    public void clearCart(long customerId) {
        executor.execute(() -> cartDao.clearCart(customerId));
    }

    public void removeProductFromCart(long customerId, long productId) {
        executor.execute(() -> cartDao.removeProductFromCart(customerId, productId));
    }

    public void deleteCartItems(long customerId, List<Long> cartItemIds) {
        executor.execute(() -> cartDao.deleteCartItems(customerId, cartItemIds));
    }

    // Transaction operations
    public LiveData<List<Transaction>> getTransactionsByCustomer(long customerId) {
        return transactionDao.getTransactionsByCustomer(customerId);
    }

    public List<Transaction> getTransactionsByCustomerSync(long customerId) {
        return transactionDao.getTransactionsByCustomerSync(customerId);
    }

    public LiveData<List<Transaction>> getTransactionsByType(String transactionType) {
        return transactionDao.getTransactionsByType(transactionType);
    }

    public LiveData<List<Transaction>> getTransactionsByDateRange(long startDate, long endDate) {
        return transactionDao.getTransactionsByDateRange(startDate, endDate);
    }

    public void insertTransaction(Transaction transaction) {
        executor.execute(() -> transactionDao.insertTransaction(transaction));
    }

    public long insertTransactionSync(Transaction transaction) {
        return transactionDao.insertTransaction(transaction);
    }

    public void updateTransaction(Transaction transaction) {
        executor.execute(() -> transactionDao.updateTransaction(transaction));
    }


    public void deleteTransaction(Transaction transaction) {
        executor.execute(() -> transactionDao.deleteTransaction(transaction));
    }
}