package com.example.cakeshop;

import android.app.AlertDialog;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import com.google.android.material.appbar.MaterialToolbar;
import com.example.cakeshop.model.Order;
import com.example.cakeshop.repository.CakeShopRepository;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Locale;

public class OrderListActivity extends AppCompatActivity {
    private CakeShopRepository repository;
    private LinearLayout orderContainer;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_order_list);

        try {
            // 1. 初始化视图组件
            orderContainer = findViewById(R.id.orderContainer);
            if (orderContainer == null) {
                throw new IllegalStateException("orderContainer view not found");
            }

            // 2. 设置Toolbar
            MaterialToolbar toolbar = findViewById(R.id.toolbar);
            setSupportActionBar(toolbar);
            if (getSupportActionBar() != null) {
                getSupportActionBar().setTitle("📋 订单管理");
            }

            // 3. 初始化Repository
            repository = new CakeShopRepository(getApplication());

            // 4. 初始化数据
            initializeOrdersObserver();

        } catch (Exception e) {
            Log.e("OrderListActivity", "初始化失败: " + e.getMessage(), e);
            Toast.makeText(this, "应用启动失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
            finish();
        }
    }

    private void initializeOrdersObserver() {
        if (repository == null) {
            Log.e("OrderListActivity", "Repository is null");
            return;
        }

        try {
            repository.getAllOrders().observe(this, this::updateOrdersList);
        } catch (Exception e) {
            Log.e("OrderListActivity", "Error setting up observer: " + e.getMessage());
            Toast.makeText(this, "无法加载订单数据", Toast.LENGTH_SHORT).show();
        }
    }

    private void updateOrdersList(List<Order> orders) {
        try {
            orderContainer.removeAllViews();

            if (orders == null || orders.isEmpty()) {
                TextView emptyText = new TextView(this);
                emptyText.setText("暂无订单数据\n点击右上角新建订单");
                emptyText.setTextSize(16);
                emptyText.setTextColor(getResources().getColor(R.color.gray, getTheme()));
                emptyText.setPadding(0, 50, 0, 50);
                emptyText.setGravity(Gravity.CENTER);
                orderContainer.addView(emptyText);
                return;
            }

            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault());
            for (Order order : orders) {
                View orderCard = createOrderCard(order, dateFormat);
                if (orderCard != null) {
                    orderContainer.addView(orderCard);
                }
            }
        } catch (Exception e) {
            Log.e("OrderListActivity", "Error updating orders list: " + e.getMessage());
            Toast.makeText(this, "更新订单列表失败", Toast.LENGTH_SHORT).show();
        }
    }

    private View createOrderCard(Order order, SimpleDateFormat dateFormat) {
        try {
            View card = getLayoutInflater().inflate(R.layout.item_order, orderContainer, false);

            TextView orderIdText = card.findViewById(R.id.orderIdText);
            TextView orderNumberText = card.findViewById(R.id.orderNumberText);
            TextView priceAndDateText = card.findViewById(R.id.priceAndDateText);
            TextView paymentStatusText = card.findViewById(R.id.paymentStatusText);

            orderIdText.setText(String.format("订单 #%d | %s", order.getId(), getOrderStatusDisplay(order.getStatus())));
            orderNumberText.setText(String.format("订单编号: %s", order.getOrderNumber()));
            priceAndDateText.setText(String.format("总金额: ¥%.2f | 下单时间: %s",
                    order.getTotalAmount(),
                    dateFormat.format(order.getOrderDate())));
            paymentStatusText.setText(String.format("支付状态: %s", getPaymentStatusDisplay(order.getPaymentStatus())));

            card.findViewById(R.id.editButton).setOnClickListener(v -> showOrderManagementDialog(order));

            return card;
        } catch (Exception e) {
            Log.e("OrderListActivity", "Error creating order card: " + e.getMessage());
            return null;
        }
    }

    private String getOrderStatusDisplay(String status) {
        try {
            return status != null ? Order.OrderStatus.valueOf(status).getDisplayName() : "未知状态";
        } catch (IllegalArgumentException e) {
            return "未知状态";
        }
    }

    private String getPaymentStatusDisplay(String status) {
        try {
            return status != null ? Order.PaymentStatus.valueOf(status).getDisplayName() : "未知状态";
        } catch (IllegalArgumentException e) {
            return "未知状态";
        }
    }

    private void showOrderManagementDialog(Order order) {
        String[] options = {
            "编辑订单",
            "更改状态",
            "标记已支付",
            "安排发货",
            "取消订单",
            "删除订单"
        };

        new AlertDialog.Builder(this)
            .setTitle("订单管理 - #" + order.getId())
            .setItems(options, (dialog, which) -> {
                switch (which) {
                    case 0: editOrder(order.getId()); break;
                    case 1: showChangeStatusDialog(order); break;
                    case 2: markOrderAsPaid(order); break;
                    case 3: markOrderAsShipped(order); break;
                    case 4: cancelOrder(order); break;
                    case 5: deleteOrder(order); break;
                }
            })
            .setNegativeButton("取消", null)
            .show();
    }

    private void editOrder(long orderId) {
        Intent intent = new Intent(this, EditOrderActivity.class);
        intent.putExtra("ORDER_ID", orderId);
        startActivity(intent);
    }

    private void showChangeStatusDialog(Order order) {
        String[] statuses = {
            "待确认", "已确认", "制作中", "待取货", "已交付", "已取消"
        };

        String[] statusValues = {
            "PENDING", "CONFIRMED", "IN_PROGRESS", 
            "READY_FOR_PICKUP", "DELIVERED", "CANCELLED"
        };

        new AlertDialog.Builder(this)
            .setTitle("更改订单状态")
            .setItems(statuses, (dialog, which) -> 
                updateOrderStatus(order, statusValues[which]))
            .setNegativeButton("取消", null)
            .show();
    }

    private void updateOrderStatus(Order order, String newStatus) {
        try {
            order.setStatus(newStatus);
            order.setUpdatedAt(System.currentTimeMillis());
            updateOrder(order, "订单状态已更新");
        } catch (Exception e) {
            Toast.makeText(this, "更新失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    private void markOrderAsPaid(Order order) {
        new AlertDialog.Builder(this)
            .setTitle("标记已支付")
            .setMessage("确定要将订单 #" + order.getId() + " 标记为已支付吗？")
            .setPositiveButton("确定", (dialog, which) -> {
                order.setPaymentStatus("PAID");
                order.setUpdatedAt(System.currentTimeMillis());
                updateOrder(order, "订单已标记为已支付");
            })
            .setNegativeButton("取消", null)
            .show();
    }

    private void markOrderAsShipped(Order order) {
        new AlertDialog.Builder(this)
            .setTitle("安排发货")
            .setMessage("确定要将订单 #" + order.getId() + " 标记为已发货吗？")
            .setPositiveButton("确定", (dialog, which) -> {
                order.setStatus("SHIPPED");
                order.setUpdatedAt(System.currentTimeMillis());
                updateOrder(order, "订单已标记为已发货");
            })
            .setNegativeButton("取消", null)
            .show();
    }

    private void cancelOrder(Order order) {
        new AlertDialog.Builder(this)
            .setTitle("取消订单")
            .setMessage("确定要取消订单 #" + order.getId() + " 吗？\n取消后订单状态将变为已取消。")
            .setPositiveButton("确定", (dialog, which) -> {
                order.setStatus("CANCELLED");
                order.setUpdatedAt(System.currentTimeMillis());
                updateOrder(order, "订单已取消");
            })
            .setNegativeButton("取消", null)
            .show();
    }

    private void deleteOrder(Order order) {
        new AlertDialog.Builder(this)
            .setTitle("删除订单")
            .setMessage("确定要删除订单 #" + order.getId() + " 吗？\n删除后将无法恢复！")
            .setPositiveButton("确定", (dialog, which) -> {
                new Thread(() -> {
                    try {
                        repository.deleteOrder(order);
                        runOnUiThread(() -> 
                            Toast.makeText(this, "订单已删除", Toast.LENGTH_SHORT).show());
                    } catch (Exception e) {
                        runOnUiThread(() -> 
                            Toast.makeText(this, "删除失败: " + e.getMessage(), Toast.LENGTH_SHORT).show());
                    }
                }).start();
            })
            .setNegativeButton("取消", null)
            .show();
    }

    private void updateOrder(Order order, String message) {
        new Thread(() -> {
            try {
                repository.updateOrder(order);
                runOnUiThread(() -> 
                    Toast.makeText(this, message, Toast.LENGTH_SHORT).show());
            } catch (Exception e) {
                runOnUiThread(() -> 
                    Toast.makeText(this, "更新失败: " + e.getMessage(), Toast.LENGTH_SHORT).show());
            }
        }).start();
    }
}