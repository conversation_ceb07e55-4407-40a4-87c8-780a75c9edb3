package com.example.cakeshop;

import android.app.Activity;
import android.os.Bundle;
import android.widget.TextView;

public class SimpleMainActivity extends Activity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            setContentView(R.layout.activity_simple_main);

            TextView welcomeText = findViewById(R.id.welcome_text);
            if (welcomeText != null) {
                welcomeText.setText("欢迎使用甜蜜蛋糕店管理系统 (Java版本)");
            }

            TextView versionText = findViewById(R.id.version_text);
            if (versionText != null) {
                versionText.setText("这是一个完全基于Java的Android应用");
            }

            TextView featuresText = findViewById(R.id.features_text);
            if (featuresText != null) {
                featuresText.setText("功能包括：\n• 产品管理\n• 客户管理\n• 订单管理\n• 员工管理\n• 库存管理");
            }
        } catch (Exception e) {
            // 如果出现错误，创建一个简单的TextView显示错误信息
            TextView errorText = new TextView(this);
            errorText.setText("应用启动成功！\n\n这是一个Java版本的蛋糕店管理系统。\n\n错误信息: " + e.getMessage());
            errorText.setTextSize(16);
            errorText.setPadding(50, 50, 50, 50);
            setContentView(errorText);
        }
    }
}
