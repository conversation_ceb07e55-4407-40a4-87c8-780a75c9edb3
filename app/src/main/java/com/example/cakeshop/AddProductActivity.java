package com.example.cakeshop;

import android.app.Activity;
import android.os.Bundle;
import android.view.View;
import android.widget.*;
import com.example.cakeshop.model.Product;
import com.example.cakeshop.repository.CakeShopRepository;

public class AddProductActivity extends Activity {

    private EditText nameEdit, descEdit, priceEdit, prepTimeEdit;
    private Spinner categorySpinner, sizeSpinner, flavorSpinner;
    private boolean isEditMode = false;
    private long productId = -1;
    private CakeShopRepository repository;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 初始化Repository
        repository = new CakeShopRepository(getApplication());

        // 检查是否为编辑模式
        isEditMode = getIntent().getBooleanExtra("EDIT_MODE", false);

        createLayout();
        setupSpinners();

        if (isEditMode) {
            loadProductData();
        }
    }

    private void createLayout() {
        ScrollView scrollView = new ScrollView(this);
        scrollView.setBackgroundColor(0xFFFCE4EC);

        LinearLayout mainLayout = new LinearLayout(this);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.setPadding(32, 32, 32, 32);

        // 标题
        TextView titleText = new TextView(this);
        titleText.setText(isEditMode ? "🍰 编辑产品" : "🍰 添加新产品");
        titleText.setTextSize(24);
        titleText.setTextColor(0xFFD81B60);
        titleText.setPadding(0, 0, 0, 32);
        mainLayout.addView(titleText);

        // 表单容器
        LinearLayout formLayout = new LinearLayout(this);
        formLayout.setOrientation(LinearLayout.VERTICAL);
        formLayout.setBackgroundColor(0xFFFFFFFF);
        formLayout.setPadding(24, 24, 24, 24);

        // 产品名称
        TextView nameLabel = new TextView(this);
        nameLabel.setText("产品名称 *");
        nameLabel.setTextSize(16);
        nameLabel.setTextColor(0xFF3E2723);
        nameLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(nameLabel);

        nameEdit = new EditText(this);
        nameEdit.setHint("请输入产品名称");
        nameEdit.setPadding(16, 16, 16, 16);
        nameEdit.setBackgroundColor(0xFFF5F5F5);
        LinearLayout.LayoutParams editParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 120);
        editParams.setMargins(0, 8, 0, 16);
        nameEdit.setLayoutParams(editParams);
        formLayout.addView(nameEdit);

        // 产品描述
        TextView descLabel = new TextView(this);
        descLabel.setText("产品描述");
        descLabel.setTextSize(16);
        descLabel.setTextColor(0xFF3E2723);
        descLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(descLabel);

        descEdit = new EditText(this);
        descEdit.setHint("请输入产品描述");
        descEdit.setPadding(16, 16, 16, 16);
        descEdit.setBackgroundColor(0xFFF5F5F5);
        descEdit.setLayoutParams(editParams);
        formLayout.addView(descEdit);

        // 价格
        TextView priceLabel = new TextView(this);
        priceLabel.setText("价格 (元) *");
        priceLabel.setTextSize(16);
        priceLabel.setTextColor(0xFF3E2723);
        priceLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(priceLabel);

        priceEdit = new EditText(this);
        priceEdit.setHint("请输入价格");
        priceEdit.setInputType(android.text.InputType.TYPE_CLASS_NUMBER | android.text.InputType.TYPE_NUMBER_FLAG_DECIMAL);
        priceEdit.setPadding(16, 16, 16, 16);
        priceEdit.setBackgroundColor(0xFFF5F5F5);
        priceEdit.setLayoutParams(editParams);
        formLayout.addView(priceEdit);

        // 类别
        TextView categoryLabel = new TextView(this);
        categoryLabel.setText("产品类别 *");
        categoryLabel.setTextSize(16);
        categoryLabel.setTextColor(0xFF3E2723);
        categoryLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(categoryLabel);

        categorySpinner = new Spinner(this);
        categorySpinner.setPadding(16, 16, 16, 16);
        categorySpinner.setBackgroundColor(0xFFF5F5F5);
        categorySpinner.setLayoutParams(editParams);
        formLayout.addView(categorySpinner);

        // 尺寸
        TextView sizeLabel = new TextView(this);
        sizeLabel.setText("蛋糕尺寸");
        sizeLabel.setTextSize(16);
        sizeLabel.setTextColor(0xFF3E2723);
        sizeLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(sizeLabel);

        sizeSpinner = new Spinner(this);
        sizeSpinner.setPadding(16, 16, 16, 16);
        sizeSpinner.setBackgroundColor(0xFFF5F5F5);
        sizeSpinner.setLayoutParams(editParams);
        formLayout.addView(sizeSpinner);

        // 口味
        TextView flavorLabel = new TextView(this);
        flavorLabel.setText("蛋糕口味");
        flavorLabel.setTextSize(16);
        flavorLabel.setTextColor(0xFF3E2723);
        flavorLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(flavorLabel);

        flavorSpinner = new Spinner(this);
        flavorSpinner.setPadding(16, 16, 16, 16);
        flavorSpinner.setBackgroundColor(0xFFF5F5F5);
        flavorSpinner.setLayoutParams(editParams);
        formLayout.addView(flavorSpinner);

        // 制作时间
        TextView prepTimeLabel = new TextView(this);
        prepTimeLabel.setText("制作时间 (分钟)");
        prepTimeLabel.setTextSize(16);
        prepTimeLabel.setTextColor(0xFF3E2723);
        prepTimeLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        formLayout.addView(prepTimeLabel);

        prepTimeEdit = new EditText(this);
        prepTimeEdit.setHint("请输入制作时间");
        prepTimeEdit.setInputType(android.text.InputType.TYPE_CLASS_NUMBER);
        prepTimeEdit.setPadding(16, 16, 16, 16);
        prepTimeEdit.setBackgroundColor(0xFFF5F5F5);
        prepTimeEdit.setLayoutParams(editParams);
        formLayout.addView(prepTimeEdit);

        // 按钮容器
        LinearLayout buttonLayout = new LinearLayout(this);
        buttonLayout.setOrientation(LinearLayout.HORIZONTAL);
        buttonLayout.setPadding(0, 24, 0, 0);

        // 取消按钮
        Button cancelButton = new Button(this);
        cancelButton.setText("取消");
        cancelButton.setTextColor(0xFF757575);
        cancelButton.setBackgroundColor(0xFFE0E0E0);
        LinearLayout.LayoutParams buttonParams = new LinearLayout.LayoutParams(0, 120, 1);
        buttonParams.setMargins(0, 0, 8, 0);
        cancelButton.setLayoutParams(buttonParams);
        cancelButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        buttonLayout.addView(cancelButton);

        // 保存按钮
        Button saveButton = new Button(this);
        saveButton.setText(isEditMode ? "更新产品" : "保存产品");
        saveButton.setTextColor(0xFFFFFFFF);
        saveButton.setBackgroundColor(0xFFD81B60);
        LinearLayout.LayoutParams saveParams = new LinearLayout.LayoutParams(0, 120, 1);
        saveParams.setMargins(8, 0, 0, 0);
        saveButton.setLayoutParams(saveParams);
        saveButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                saveProduct();
            }
        });
        buttonLayout.addView(saveButton);

        formLayout.addView(buttonLayout);
        mainLayout.addView(formLayout);
        scrollView.addView(mainLayout);
        setContentView(scrollView);
    }

    private void setupSpinners() {
        // 类别选项
        String[] categories = new String[Product.ProductCategory.values().length];
        int i = 0;
        for (Product.ProductCategory category : Product.ProductCategory.values()) {
            categories[i++] = category.getDisplayName();
        }
        ArrayAdapter<String> categoryAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, categories);
        categoryAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        categorySpinner.setAdapter(categoryAdapter);

        // 尺寸选项
        String[] sizes = new String[Product.CakeSize.values().length];
        i = 0;
        for (Product.CakeSize size : Product.CakeSize.values()) {
            sizes[i++] = size.getDisplayName();
        }
        ArrayAdapter<String> sizeAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, sizes);
        sizeAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        sizeSpinner.setAdapter(sizeAdapter);

        // 口味选项
        String[] flavors = new String[Product.CakeFlavor.values().length];
        i = 0;
        for (Product.CakeFlavor flavor : Product.CakeFlavor.values()) {
            flavors[i++] = flavor.getDisplayName();
        }
        ArrayAdapter<String> flavorAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, flavors);
        flavorAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        flavorSpinner.setAdapter(flavorAdapter);
    }

    private void saveProduct() {
        String name = nameEdit.getText().toString().trim();
        String description = descEdit.getText().toString().trim();
        String priceStr = priceEdit.getText().toString().trim();
        String prepTimeStr = prepTimeEdit.getText().toString().trim();

        if (name.isEmpty() || priceStr.isEmpty()) {
            Toast.makeText(this, "请填写必填项目", Toast.LENGTH_SHORT).show();
            return;
        }

        try {
            double price = Double.parseDouble(priceStr);
            int prepTime = prepTimeStr.isEmpty() ? 60 : Integer.parseInt(prepTimeStr);

            String categoryDisplay = categorySpinner.getSelectedItem().toString();
            String sizeDisplay = sizeSpinner.getSelectedItem().toString();
            String flavorDisplay = flavorSpinner.getSelectedItem().toString();

            String category = getCategoryEnumName(categoryDisplay);
            String size = getSizeEnumName(sizeDisplay);
            String flavor = getFlavorEnumName(flavorDisplay);

            if (isEditMode) {
                // 更新现有产品
                Product product = new Product();
                product.setProductId(productId);  // 设置ID
                product.setName(name);
                product.setDescription(description);
                product.setPrice(price);
                product.setCategory(category);
                product.setSize(size);
                product.setFlavor(flavor);
                product.setPreparationTime(prepTime);

                // 保持原有的状态
                Boolean originalAvailable = getIntent().getBooleanExtra("PRODUCT_AVAILABLE", true);
                long originalCreatedAt = getIntent().getLongExtra("PRODUCT_CREATED_AT", System.currentTimeMillis());
                product.setAvailable(originalAvailable);
                product.setCreatedAt(originalCreatedAt);

                // 更新修改时间为当前时间
                product.setUpdatedAt(System.currentTimeMillis());

                // 设置其他默认值
                product.setImageUrl("");

                repository.updateProduct(product);
                Toast.makeText(this, "产品 \"" + name + "\" 更新成功！", Toast.LENGTH_LONG).show();
            } else {
                // 添加新产品
                Product product = new Product();
                product.setName(name);
                product.setDescription(description);
                product.setPrice(price);
                product.setCategory(category);
                product.setSize(size);
                product.setFlavor(flavor);
                product.setPreparationTime(prepTime);
                repository.insertProduct(product);
                Toast.makeText(this, "产品 \"" + name + "\" 添加成功！", Toast.LENGTH_LONG).show();
            }

            finish();

        } catch (NumberFormatException e) {
            Toast.makeText(this, "请输入正确的数字格式", Toast.LENGTH_SHORT).show();
        }
    }

    private void loadProductData() {
        productId = getIntent().getLongExtra("PRODUCT_ID", -1);
        String name = getIntent().getStringExtra("PRODUCT_NAME");
        String description = getIntent().getStringExtra("PRODUCT_DESC");
        double price = getIntent().getDoubleExtra("PRODUCT_PRICE", 0.0);
        String category = getIntent().getStringExtra("PRODUCT_CATEGORY");
        String size = getIntent().getStringExtra("PRODUCT_SIZE");
        String flavor = getIntent().getStringExtra("PRODUCT_FLAVOR");
        int prepTime = getIntent().getIntExtra("PRODUCT_PREP_TIME", 60);
        boolean isAvailable = getIntent().getBooleanExtra("PRODUCT_AVAILABLE", true);
        long createdAt = getIntent().getLongExtra("PRODUCT_CREATED_AT", System.currentTimeMillis());

        // 填充表单数据
        nameEdit.setText(name);
        descEdit.setText(description);
        priceEdit.setText(String.valueOf(price));
        prepTimeEdit.setText(String.valueOf(prepTime));

        // 设置Spinner选择项
        setSpinnerSelection(categorySpinner, category);
        setSpinnerSelection(sizeSpinner, size);
        setSpinnerSelection(flavorSpinner, flavor);
    }

    private void setSpinnerSelection(Spinner spinner, String value) {
        if (value != null && spinner.getAdapter() != null) {
            for (int i = 0; i < spinner.getAdapter().getCount(); i++) {
                if (spinner.getAdapter().getItem(i).toString().equals(value)) {
                    spinner.setSelection(i);
                    break;
                }
            }
        }
    }

    private String getCategoryEnumName(String displayName) {
        for (Product.ProductCategory category : Product.ProductCategory.values()) {
            if (category.getDisplayName().equals(displayName)) {
                return category.name();
            }
        }
        return Product.ProductCategory.BIRTHDAY_CAKE.name();
    }

    private String getSizeEnumName(String displayName) {
        for (Product.CakeSize size : Product.CakeSize.values()) {
            if (size.getDisplayName().equals(displayName)) {
                return size.name();
            }
        }
        return Product.CakeSize.MEDIUM.name();
    }

    private String getFlavorEnumName(String displayName) {
        for (Product.CakeFlavor flavor : Product.CakeFlavor.values()) {
            if (flavor.getDisplayName().equals(displayName)) {
                return flavor.name();
            }
        }
        return Product.CakeFlavor.VANILLA.name();
    }
}