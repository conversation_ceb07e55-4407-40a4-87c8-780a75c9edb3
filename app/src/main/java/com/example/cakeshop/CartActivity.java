package com.example.cakeshop;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.*;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.example.cakeshop.database.CakeShopDatabase;
import com.example.cakeshop.model.Cart;
import com.example.cakeshop.model.Product;
import com.example.cakeshop.model.Customer;
import com.example.cakeshop.model.Order;
import com.example.cakeshop.model.OrderItem;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class CartActivity extends Activity {
    
    private RecyclerView cartRecyclerView;
    private TextView totalAmountText, emptyCartText;
    private Button checkoutButton, clearCartButton, backButton;
    private CartAdapter cartAdapter;
    private List<Cart> cartItems;
    
    private CakeShopDatabase database;
    private ExecutorService executor;
    private long currentCustomerId;
    private double totalAmount = 0.0;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        database = CakeShopDatabase.getDatabase(this);
        executor = Executors.newSingleThreadExecutor();
        currentCustomerId = getIntent().getLongExtra("customer_id", 1);
        
        createLayout();
        loadCartItems();
    }
    
    private void createLayout() {
        LinearLayout mainLayout = new LinearLayout(this);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.setBackgroundColor(0xFFF5F5F5);
        
        // 标题栏
        LinearLayout headerLayout = new LinearLayout(this);
        headerLayout.setOrientation(LinearLayout.HORIZONTAL);
        headerLayout.setBackgroundColor(0xFFD81B60);
        headerLayout.setPadding(16, 16, 16, 16);
        
        TextView titleText = new TextView(this);
        titleText.setText("🛒 购物车");
        titleText.setTextSize(20);
        titleText.setTextColor(0xFFFFFFFF);
        titleText.setTypeface(null, android.graphics.Typeface.BOLD);
        headerLayout.addView(titleText);
        
        mainLayout.addView(headerLayout);
        
        // 购物车列表
        cartRecyclerView = new RecyclerView(this);
        cartRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        cartRecyclerView.setPadding(16, 16, 16, 16);
        LinearLayout.LayoutParams recyclerParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 0, 1.0f);
        cartRecyclerView.setLayoutParams(recyclerParams);
        mainLayout.addView(cartRecyclerView);
        
        // 空购物车提示
        emptyCartText = new TextView(this);
        emptyCartText.setText("购物车是空的\n快去选购您喜欢的蛋糕吧！");
        emptyCartText.setTextSize(16);
        emptyCartText.setTextColor(0xFF888888);
        emptyCartText.setGravity(android.view.Gravity.CENTER);
        emptyCartText.setPadding(32, 64, 32, 64);
        emptyCartText.setVisibility(View.GONE);
        mainLayout.addView(emptyCartText);
        
        // 底部区域
        LinearLayout bottomLayout = new LinearLayout(this);
        bottomLayout.setOrientation(LinearLayout.VERTICAL);
        bottomLayout.setBackgroundColor(0xFFFFFFFF);
        bottomLayout.setPadding(16, 16, 16, 16);
        
        // 总金额
        totalAmountText = new TextView(this);
        totalAmountText.setText("总计: ¥0.00");
        totalAmountText.setTextSize(20);
        totalAmountText.setTextColor(0xFFD81B60);
        totalAmountText.setTypeface(null, android.graphics.Typeface.BOLD);
        totalAmountText.setGravity(android.view.Gravity.CENTER);
        totalAmountText.setPadding(0, 0, 0, 16);
        bottomLayout.addView(totalAmountText);
        
        // 按钮区域
        LinearLayout buttonLayout = new LinearLayout(this);
        buttonLayout.setOrientation(LinearLayout.HORIZONTAL);
        
        backButton = new Button(this);
        backButton.setText("继续购物");
        backButton.setTextColor(0xFF666666);
        backButton.setBackgroundColor(0xFFE0E0E0);
        LinearLayout.LayoutParams backParams = new LinearLayout.LayoutParams(
            0, 120, 1.0f);
        backParams.setMargins(0, 0, 8, 0);
        backButton.setLayoutParams(backParams);
        backButton.setOnClickListener(v -> finish());
        buttonLayout.addView(backButton);
        
        clearCartButton = new Button(this);
        clearCartButton.setText("清空购物车");
        clearCartButton.setTextColor(0xFFFFFFFF);
        clearCartButton.setBackgroundColor(0xFFFF5722);
        LinearLayout.LayoutParams clearParams = new LinearLayout.LayoutParams(
            0, 120, 1.0f);
        clearParams.setMargins(4, 0, 4, 0);
        clearCartButton.setLayoutParams(clearParams);
        clearCartButton.setOnClickListener(v -> clearCart());
        buttonLayout.addView(clearCartButton);
        
        checkoutButton = new Button(this);
        checkoutButton.setText("结算");
        checkoutButton.setTextColor(0xFFFFFFFF);
        checkoutButton.setBackgroundColor(0xFFD81B60);
        LinearLayout.LayoutParams checkoutParams = new LinearLayout.LayoutParams(
            0, 120, 1.0f);
        checkoutParams.setMargins(8, 0, 0, 0);
        checkoutButton.setLayoutParams(checkoutParams);
        checkoutButton.setOnClickListener(v -> checkout());
        buttonLayout.addView(checkoutButton);
        
        bottomLayout.addView(buttonLayout);
        mainLayout.addView(bottomLayout);
        
        setContentView(mainLayout);
    }
    
    private void loadCartItems() {
        executor.execute(() -> {
            try {
                List<Cart> items = database.cartDao().getCartByCustomerSync(currentCustomerId);
                runOnUiThread(() -> {
                    cartItems = items;
                    if (items.isEmpty()) {
                        showEmptyCart();
                    } else {
                        showCartItems();
                    }
                    calculateTotal();
                });
            } catch (Exception e) {
                runOnUiThread(() -> {
                    Toast.makeText(this, "加载购物车失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                });
            }
        });
    }
    
    private void showEmptyCart() {
        cartRecyclerView.setVisibility(View.GONE);
        emptyCartText.setVisibility(View.VISIBLE);
        checkoutButton.setEnabled(false);
        clearCartButton.setEnabled(false);
    }
    
    private void showCartItems() {
        cartRecyclerView.setVisibility(View.VISIBLE);
        emptyCartText.setVisibility(View.GONE);
        checkoutButton.setEnabled(true);
        clearCartButton.setEnabled(true);
        
        cartAdapter = new CartAdapter(cartItems);
        cartRecyclerView.setAdapter(cartAdapter);
    }
    
    private void calculateTotal() {
        totalAmount = 0.0;
        for (Cart item : cartItems) {
            totalAmount += item.getSubtotal();
        }
        totalAmountText.setText("总计: ¥" + String.format("%.2f", totalAmount));
    }
    
    private void clearCart() {
        new android.app.AlertDialog.Builder(this)
            .setTitle("清空购物车")
            .setMessage("确定要清空购物车吗？")
            .setPositiveButton("确定", (dialog, which) -> {
                executor.execute(() -> {
                    database.cartDao().clearCart(currentCustomerId);
                    runOnUiThread(() -> {
                        loadCartItems();
                        Toast.makeText(this, "购物车已清空", Toast.LENGTH_SHORT).show();
                    });
                });
            })
            .setNegativeButton("取消", null)
            .show();
    }
    
    private void checkout() {
        if (cartItems.isEmpty()) {
            Toast.makeText(this, "购物车是空的", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // 跳转到结算页面
        Intent intent = new Intent(this, CheckoutActivity.class);
        intent.putExtra("customer_id", currentCustomerId);
        intent.putExtra("total_amount", totalAmount);
        startActivity(intent);
    }
    
    private void updateCartItem(Cart item) {
        executor.execute(() -> {
            database.cartDao().updateCartItem(item);
            runOnUiThread(() -> {
                calculateTotal();
                cartAdapter.notifyDataSetChanged();
            });
        });
    }
    
    private void removeCartItem(Cart item) {
        executor.execute(() -> {
            database.cartDao().deleteCartItem(item);
            runOnUiThread(() -> {
                cartItems.remove(item);
                if (cartItems.isEmpty()) {
                    showEmptyCart();
                }
                calculateTotal();
                cartAdapter.notifyDataSetChanged();
                Toast.makeText(this, "已移除商品", Toast.LENGTH_SHORT).show();
            });
        });
    }
    
    // 购物车适配器
    private class CartAdapter extends RecyclerView.Adapter<CartAdapter.CartViewHolder> {
        private List<Cart> items;
        
        public CartAdapter(List<Cart> items) {
            this.items = items;
        }
        
        @Override
        public CartViewHolder onCreateViewHolder(android.view.ViewGroup parent, int viewType) {
            LinearLayout itemLayout = new LinearLayout(CartActivity.this);
            itemLayout.setOrientation(LinearLayout.VERTICAL);
            itemLayout.setBackgroundColor(0xFFFFFFFF);
            itemLayout.setPadding(16, 16, 16, 16);
            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
            layoutParams.setMargins(0, 0, 0, 8);
            itemLayout.setLayoutParams(layoutParams);
            
            return new CartViewHolder(itemLayout);
        }
        
        @Override
        public void onBindViewHolder(CartViewHolder holder, int position) {
            Cart item = items.get(position);
            holder.bind(item);
        }
        
        @Override
        public int getItemCount() {
            return items.size();
        }
        
        class CartViewHolder extends RecyclerView.ViewHolder {
            private TextView nameText, priceText, subtotalText;
            private Button decreaseButton, increaseButton, removeButton;
            private TextView quantityText;
            
            public CartViewHolder(View itemView) {
                super(itemView);
                
                LinearLayout layout = (LinearLayout) itemView;
                
                // 商品信息
                LinearLayout infoLayout = new LinearLayout(CartActivity.this);
                infoLayout.setOrientation(LinearLayout.HORIZONTAL);
                
                LinearLayout textLayout = new LinearLayout(CartActivity.this);
                textLayout.setOrientation(LinearLayout.VERTICAL);
                LinearLayout.LayoutParams textParams = new LinearLayout.LayoutParams(
                    0, LinearLayout.LayoutParams.WRAP_CONTENT, 1.0f);
                textLayout.setLayoutParams(textParams);
                
                nameText = new TextView(CartActivity.this);
                nameText.setTextSize(16);
                nameText.setTextColor(0xFF333333);
                nameText.setTypeface(null, android.graphics.Typeface.BOLD);
                textLayout.addView(nameText);
                
                priceText = new TextView(CartActivity.this);
                priceText.setTextSize(14);
                priceText.setTextColor(0xFF666666);
                textLayout.addView(priceText);
                
                subtotalText = new TextView(CartActivity.this);
                subtotalText.setTextSize(16);
                subtotalText.setTextColor(0xFFD81B60);
                subtotalText.setTypeface(null, android.graphics.Typeface.BOLD);
                textLayout.addView(subtotalText);
                
                infoLayout.addView(textLayout);
                
                removeButton = new Button(CartActivity.this);
                removeButton.setText("移除");
                removeButton.setTextColor(0xFFFFFFFF);
                removeButton.setBackgroundColor(0xFFFF5722);
                LinearLayout.LayoutParams removeParams = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT, 80);
                removeButton.setLayoutParams(removeParams);
                infoLayout.addView(removeButton);
                
                layout.addView(infoLayout);
                
                // 数量控制
                LinearLayout quantityLayout = new LinearLayout(CartActivity.this);
                quantityLayout.setOrientation(LinearLayout.HORIZONTAL);
                quantityLayout.setGravity(android.view.Gravity.CENTER_VERTICAL);
                quantityLayout.setPadding(0, 8, 0, 0);
                
                TextView quantityLabel = new TextView(CartActivity.this);
                quantityLabel.setText("数量: ");
                quantityLabel.setTextSize(14);
                quantityLabel.setTextColor(0xFF666666);
                quantityLayout.addView(quantityLabel);
                
                decreaseButton = new Button(CartActivity.this);
                decreaseButton.setText("-");
                decreaseButton.setTextSize(18);
                decreaseButton.setTextColor(0xFFFFFFFF);
                decreaseButton.setBackgroundColor(0xFF666666);
                LinearLayout.LayoutParams decreaseParams = new LinearLayout.LayoutParams(80, 80);
                decreaseParams.setMargins(8, 0, 4, 0);
                decreaseButton.setLayoutParams(decreaseParams);
                quantityLayout.addView(decreaseButton);
                
                quantityText = new TextView(CartActivity.this);
                quantityText.setTextSize(16);
                quantityText.setTextColor(0xFF333333);
                quantityText.setGravity(android.view.Gravity.CENTER);
                quantityText.setMinWidth(60);
                quantityLayout.addView(quantityText);
                
                increaseButton = new Button(CartActivity.this);
                increaseButton.setText("+");
                increaseButton.setTextSize(18);
                increaseButton.setTextColor(0xFFFFFFFF);
                increaseButton.setBackgroundColor(0xFF666666);
                LinearLayout.LayoutParams increaseParams = new LinearLayout.LayoutParams(80, 80);
                increaseParams.setMargins(4, 0, 8, 0);
                increaseButton.setLayoutParams(increaseParams);
                quantityLayout.addView(increaseButton);
                
                layout.addView(quantityLayout);
            }
            
            public void bind(Cart item) {
                // 这里需要获取产品信息，简化处理
                nameText.setText("产品ID: " + item.getProductId());
                priceText.setText("单价: ¥" + String.format("%.2f", item.getUnitPrice()));
                subtotalText.setText("小计: ¥" + String.format("%.2f", item.getSubtotal()));
                quantityText.setText(String.valueOf(item.getQuantity()));
                
                decreaseButton.setOnClickListener(v -> {
                    if (item.getQuantity() > 1) {
                        item.decreaseQuantity();
                        updateCartItem(item);
                    }
                });
                
                increaseButton.setOnClickListener(v -> {
                    item.increaseQuantity();
                    updateCartItem(item);
                });
                
                removeButton.setOnClickListener(v -> removeCartItem(item));
            }
        }
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
}
