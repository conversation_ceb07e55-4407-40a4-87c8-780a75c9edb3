package com.example.cakeshop;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.*;
import com.example.cakeshop.database.CakeShopDatabase;
import com.example.cakeshop.model.Customer;
import com.example.cakeshop.model.User;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class RegisterActivity extends Activity {
    
    private EditText usernameEdit, passwordEdit, confirmPasswordEdit;
    private EditText fullNameEdit, phoneEdit, emailEdit, addressEdit;
    private Button registerButton, backToLoginButton;
    private TextView titleText;
    private CakeShopDatabase database;
    private ExecutorService executor;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        database = CakeShopDatabase.getDatabase(this);
        executor = Executors.newSingleThreadExecutor();
        
        createLayout();
    }
    
    private void createLayout() {
        ScrollView scrollView = new ScrollView(this);
        scrollView.setBackgroundColor(0xFFFCE4EC);
        
        LinearLayout mainLayout = new LinearLayout(this);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.setPadding(48, 80, 48, 80);
        mainLayout.setGravity(android.view.Gravity.CENTER);
        
        // Logo区域
        TextView logoText = new TextView(this);
        logoText.setText("🎂");
        logoText.setTextSize(64);
        logoText.setGravity(android.view.Gravity.CENTER);
        LinearLayout.LayoutParams logoParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        logoParams.setMargins(0, 0, 0, 24);
        logoText.setLayoutParams(logoParams);
        mainLayout.addView(logoText);
        
        // 标题
        titleText = new TextView(this);
        titleText.setText("用户注册");
        titleText.setTextSize(24);
        titleText.setTextColor(0xFFD81B60);
        titleText.setTypeface(null, android.graphics.Typeface.BOLD);
        titleText.setGravity(android.view.Gravity.CENTER);
        LinearLayout.LayoutParams titleParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        titleParams.setMargins(0, 0, 0, 32);
        titleText.setLayoutParams(titleParams);
        mainLayout.addView(titleText);
        
        // 注册表单容器
        LinearLayout formLayout = new LinearLayout(this);
        formLayout.setOrientation(LinearLayout.VERTICAL);
        formLayout.setBackgroundColor(0xFFFFFFFF);
        formLayout.setPadding(32, 32, 32, 32);
        LinearLayout.LayoutParams formParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        formParams.setMargins(0, 0, 0, 24);
        formLayout.setLayoutParams(formParams);
        
        LinearLayout.LayoutParams editParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 120);
        editParams.setMargins(0, 8, 0, 16);
        
        // 用户名
        addFormField(formLayout, "用户名", editParams);
        usernameEdit = (EditText) formLayout.getChildAt(formLayout.getChildCount() - 1);
        usernameEdit.setHint("请输入用户名");
        
        // 密码
        addFormField(formLayout, "密码", editParams);
        passwordEdit = (EditText) formLayout.getChildAt(formLayout.getChildCount() - 1);
        passwordEdit.setHint("请输入密码");
        passwordEdit.setInputType(android.text.InputType.TYPE_CLASS_TEXT | android.text.InputType.TYPE_TEXT_VARIATION_PASSWORD);
        
        // 确认密码
        addFormField(formLayout, "确认密码", editParams);
        confirmPasswordEdit = (EditText) formLayout.getChildAt(formLayout.getChildCount() - 1);
        confirmPasswordEdit.setHint("请再次输入密码");
        confirmPasswordEdit.setInputType(android.text.InputType.TYPE_CLASS_TEXT | android.text.InputType.TYPE_TEXT_VARIATION_PASSWORD);
        
        // 姓名
        addFormField(formLayout, "姓名", editParams);
        fullNameEdit = (EditText) formLayout.getChildAt(formLayout.getChildCount() - 1);
        fullNameEdit.setHint("请输入真实姓名");
        
        // 手机号
        addFormField(formLayout, "手机号", editParams);
        phoneEdit = (EditText) formLayout.getChildAt(formLayout.getChildCount() - 1);
        phoneEdit.setHint("请输入手机号");
        phoneEdit.setInputType(android.text.InputType.TYPE_CLASS_PHONE);
        
        // 邮箱
        addFormField(formLayout, "邮箱", editParams);
        emailEdit = (EditText) formLayout.getChildAt(formLayout.getChildCount() - 1);
        emailEdit.setHint("请输入邮箱地址");
        emailEdit.setInputType(android.text.InputType.TYPE_TEXT_VARIATION_EMAIL_ADDRESS);
        
        // 地址
        addFormField(formLayout, "地址", editParams);
        addressEdit = (EditText) formLayout.getChildAt(formLayout.getChildCount() - 1);
        addressEdit.setHint("请输入地址");
        
        // 注册按钮
        registerButton = new Button(this);
        registerButton.setText("注册");
        registerButton.setTextSize(18);
        registerButton.setTextColor(0xFFFFFFFF);
        registerButton.setBackgroundColor(0xFFD81B60);
        LinearLayout.LayoutParams buttonParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 120);
        buttonParams.setMargins(0, 24, 0, 16);
        registerButton.setLayoutParams(buttonParams);
        registerButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                performRegister();
            }
        });
        formLayout.addView(registerButton);
        
        // 返回登录按钮
        backToLoginButton = new Button(this);
        backToLoginButton.setText("返回登录");
        backToLoginButton.setTextSize(16);
        backToLoginButton.setTextColor(0xFFD81B60);
        backToLoginButton.setBackgroundColor(0xFFF5F5F5);
        LinearLayout.LayoutParams backButtonParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 100);
        backToLoginButton.setLayoutParams(backButtonParams);
        backToLoginButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        formLayout.addView(backToLoginButton);
        
        mainLayout.addView(formLayout);
        scrollView.addView(mainLayout);
        setContentView(scrollView);
    }
    
    private void addFormField(LinearLayout parent, String labelText, LinearLayout.LayoutParams editParams) {
        TextView label = new TextView(this);
        label.setText(labelText);
        label.setTextSize(16);
        label.setTextColor(0xFF3E2723);
        label.setTypeface(null, android.graphics.Typeface.BOLD);
        parent.addView(label);
        
        EditText editText = new EditText(this);
        editText.setPadding(16, 16, 16, 16);
        editText.setBackgroundColor(0xFFF5F5F5);
        editText.setLayoutParams(editParams);
        parent.addView(editText);
    }
    
    private void performRegister() {
        String username = usernameEdit.getText().toString().trim();
        String password = passwordEdit.getText().toString().trim();
        String confirmPassword = confirmPasswordEdit.getText().toString().trim();
        String fullName = fullNameEdit.getText().toString().trim();
        String phone = phoneEdit.getText().toString().trim();
        String email = emailEdit.getText().toString().trim();
        String address = addressEdit.getText().toString().trim();
        
        // 验证输入
        if (username.isEmpty() || password.isEmpty() || fullName.isEmpty() || phone.isEmpty()) {
            Toast.makeText(this, "请填写必填项（用户名、密码、姓名、手机号）", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (!password.equals(confirmPassword)) {
            Toast.makeText(this, "两次输入的密码不一致", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (password.length() < 6) {
            Toast.makeText(this, "密码长度至少6位", Toast.LENGTH_SHORT).show();
            return;
        }
        
        registerButton.setEnabled(false);
        registerButton.setText("注册中...");
        
        executor.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    // 检查用户名是否已存在
                    User existingUser = database.userDao().getUserByUsername(username);
                    if (existingUser != null) {
                        runOnUiThread(() -> {
                            registerButton.setEnabled(true);
                            registerButton.setText("注册");
                            Toast.makeText(RegisterActivity.this, "用户名已存在", Toast.LENGTH_SHORT).show();
                        });
                        return;
                    }
                    
                    // 创建用户账户
                    User newUser = new User(username, password, fullName, User.UserRole.EMPLOYEE);
                    long userId = database.userDao().insertUser(newUser);
                    
                    // 创建客户档案
                    Customer newCustomer = new Customer(fullName, phone);
                    newCustomer.setEmail(email);
                    newCustomer.setAddress(address);
                    database.customerDao().insertCustomer(newCustomer);
                    
                    runOnUiThread(() -> {
                        Toast.makeText(RegisterActivity.this, "注册成功！请登录", Toast.LENGTH_SHORT).show();
                        finish();
                    });
                    
                } catch (Exception e) {
                    runOnUiThread(() -> {
                        registerButton.setEnabled(true);
                        registerButton.setText("注册");
                        Toast.makeText(RegisterActivity.this, "注册失败：" + e.getMessage(), Toast.LENGTH_SHORT).show();
                    });
                }
            }
        });
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
}
