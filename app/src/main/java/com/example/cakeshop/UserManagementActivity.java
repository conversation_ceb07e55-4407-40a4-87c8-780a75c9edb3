package com.example.cakeshop;

import androidx.appcompat.app.AppCompatActivity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.*;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.example.cakeshop.database.CakeShopDatabase;
import com.example.cakeshop.model.User;
import com.example.cakeshop.model.Customer;
import com.example.cakeshop.utils.SessionManager;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class UserManagementActivity extends AppCompatActivity {
    
    private RecyclerView usersRecyclerView, customersRecyclerView;
    private Button addUserButton, refreshButton, backButton;
    private TextView usersCountText, customersCountText;
    private UserAdapter userAdapter;
    private CustomerAdapter customerAdapter;
    private List<User> userList;
    private List<Customer> customerList;
    
    private CakeShopDatabase database;
    private SessionManager sessionManager;
    private ExecutorService executor;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        database = CakeShopDatabase.getDatabase(this);
        sessionManager = new SessionManager(this);
        executor = Executors.newSingleThreadExecutor();
        
        // 检查管理员权限
        if (!sessionManager.isAdmin()) {
            Toast.makeText(this, "您没有权限访问用户管理", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }
        
        createLayout();
        loadData();
    }
    
    private void createLayout() {
        ScrollView scrollView = new ScrollView(this);
        scrollView.setBackgroundColor(0xFFF5F5F5);
        
        LinearLayout mainLayout = new LinearLayout(this);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        
        // 标题栏
        LinearLayout headerLayout = new LinearLayout(this);
        headerLayout.setOrientation(LinearLayout.HORIZONTAL);
        headerLayout.setBackgroundColor(0xFFD81B60);
        headerLayout.setPadding(16, 16, 16, 16);
        
        TextView titleText = new TextView(this);
        titleText.setText("👥 用户管理");
        titleText.setTextSize(20);
        titleText.setTextColor(0xFFFFFFFF);
        titleText.setTypeface(null, android.graphics.Typeface.BOLD);
        LinearLayout.LayoutParams titleParams = new LinearLayout.LayoutParams(
            0, LinearLayout.LayoutParams.WRAP_CONTENT, 1.0f);
        titleText.setLayoutParams(titleParams);
        headerLayout.addView(titleText);
        
        refreshButton = new Button(this);
        refreshButton.setText("刷新");
        refreshButton.setTextColor(0xFFD81B60);
        refreshButton.setBackgroundColor(0xFFFFFFFF);
        LinearLayout.LayoutParams refreshParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        refreshButton.setLayoutParams(refreshParams);
        refreshButton.setOnClickListener(v -> loadData());
        headerLayout.addView(refreshButton);
        
        mainLayout.addView(headerLayout);
        
        // 系统用户管理卡片
        LinearLayout usersCard = createCard();
        
        LinearLayout usersTitleLayout = new LinearLayout(this);
        usersTitleLayout.setOrientation(LinearLayout.HORIZONTAL);
        
        TextView usersTitle = new TextView(this);
        usersTitle.setText("🔐 系统用户");
        usersTitle.setTextSize(18);
        usersTitle.setTextColor(0xFF333333);
        usersTitle.setTypeface(null, android.graphics.Typeface.BOLD);
        LinearLayout.LayoutParams usersTitleParams = new LinearLayout.LayoutParams(
            0, LinearLayout.LayoutParams.WRAP_CONTENT, 1.0f);
        usersTitle.setLayoutParams(usersTitleParams);
        usersTitleLayout.addView(usersTitle);
        
        usersCountText = new TextView(this);
        usersCountText.setText("总数: 0");
        usersCountText.setTextSize(14);
        usersCountText.setTextColor(0xFF666666);
        usersTitleLayout.addView(usersCountText);
        
        usersCard.addView(usersTitleLayout);
        
        addUserButton = new Button(this);
        addUserButton.setText("+ 添加用户");
        addUserButton.setTextColor(0xFFFFFFFF);
        addUserButton.setBackgroundColor(0xFFD81B60);
        LinearLayout.LayoutParams addUserParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 100);
        addUserParams.setMargins(0, 16, 0, 16);
        addUserButton.setLayoutParams(addUserParams);
        addUserButton.setOnClickListener(v -> showAddUserDialog());
        usersCard.addView(addUserButton);
        
        usersRecyclerView = new RecyclerView(this);
        usersRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        LinearLayout.LayoutParams usersRecyclerParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 400);
        usersRecyclerView.setLayoutParams(usersRecyclerParams);
        usersCard.addView(usersRecyclerView);
        
        mainLayout.addView(usersCard);
        
        // 客户管理卡片
        LinearLayout customersCard = createCard();
        
        LinearLayout customersTitleLayout = new LinearLayout(this);
        customersTitleLayout.setOrientation(LinearLayout.HORIZONTAL);
        
        TextView customersTitle = new TextView(this);
        customersTitle.setText("👤 客户管理");
        customersTitle.setTextSize(18);
        customersTitle.setTextColor(0xFF333333);
        customersTitle.setTypeface(null, android.graphics.Typeface.BOLD);
        LinearLayout.LayoutParams customersTitleParams = new LinearLayout.LayoutParams(
            0, LinearLayout.LayoutParams.WRAP_CONTENT, 1.0f);
        customersTitle.setLayoutParams(customersTitleParams);
        customersTitleLayout.addView(customersTitle);
        
        customersCountText = new TextView(this);
        customersCountText.setText("总数: 0");
        customersCountText.setTextSize(14);
        customersCountText.setTextColor(0xFF666666);
        customersTitleLayout.addView(customersCountText);
        
        customersCard.addView(customersTitleLayout);
        
        customersRecyclerView = new RecyclerView(this);
        customersRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        LinearLayout.LayoutParams customersRecyclerParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 400);
        customersRecyclerView.setLayoutParams(customersRecyclerParams);
        customersCard.addView(customersRecyclerView);
        
        mainLayout.addView(customersCard);
        
        // 返回按钮
        backButton = new Button(this);
        backButton.setText("返回");
        backButton.setTextColor(0xFF666666);
        backButton.setBackgroundColor(0xFFE0E0E0);
        LinearLayout.LayoutParams backParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 120);
        backParams.setMargins(16, 16, 16, 16);
        backButton.setLayoutParams(backParams);
        backButton.setOnClickListener(v -> finish());
        mainLayout.addView(backButton);
        
        scrollView.addView(mainLayout);
        setContentView(scrollView);
    }
    
    private LinearLayout createCard() {
        LinearLayout card = new LinearLayout(this);
        card.setOrientation(LinearLayout.VERTICAL);
        card.setBackgroundColor(0xFFFFFFFF);
        card.setPadding(16, 16, 16, 16);
        LinearLayout.LayoutParams cardParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        cardParams.setMargins(16, 8, 16, 8);
        card.setLayoutParams(cardParams);
        return card;
    }
    
    private void loadData() {
        executor.execute(() -> {
            try {
                // 加载用户数据
                database.userDao().getAllUsers().observe(this, users -> {
                    userList = new ArrayList<>(users);
                    usersCountText.setText("总数: " + userList.size());
                    userAdapter = new UserAdapter(userList);
                    usersRecyclerView.setAdapter(userAdapter);
                });
                
                // 加载客户数据
                database.customerDao().getAllCustomers().observe(this, customers -> {
                    customerList = new ArrayList<>(customers);
                    customersCountText.setText("总数: " + customerList.size());
                    customerAdapter = new CustomerAdapter(customerList);
                    customersRecyclerView.setAdapter(customerAdapter);
                });
                
            } catch (Exception e) {
                runOnUiThread(() -> {
                    Toast.makeText(this, "加载数据失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                });
            }
        });
    }
    
    private void showAddUserDialog() {
        LinearLayout dialogLayout = new LinearLayout(this);
        dialogLayout.setOrientation(LinearLayout.VERTICAL);
        dialogLayout.setPadding(32, 32, 32, 32);
        
        EditText usernameEdit = new EditText(this);
        usernameEdit.setHint("用户名");
        usernameEdit.setPadding(16, 16, 16, 16);
        dialogLayout.addView(usernameEdit);
        
        EditText passwordEdit = new EditText(this);
        passwordEdit.setHint("密码");
        passwordEdit.setInputType(android.text.InputType.TYPE_CLASS_TEXT | android.text.InputType.TYPE_TEXT_VARIATION_PASSWORD);
        passwordEdit.setPadding(16, 16, 16, 16);
        dialogLayout.addView(passwordEdit);
        
        EditText fullNameEdit = new EditText(this);
        fullNameEdit.setHint("姓名");
        fullNameEdit.setPadding(16, 16, 16, 16);
        dialogLayout.addView(fullNameEdit);
        
        Spinner roleSpinner = new Spinner(this);
        String[] roles = {"员工", "管理员"};
        ArrayAdapter<String> adapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, roles);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        roleSpinner.setAdapter(adapter);
        dialogLayout.addView(roleSpinner);
        
        new android.app.AlertDialog.Builder(this)
            .setTitle("添加用户")
            .setView(dialogLayout)
            .setPositiveButton("添加", (dialog, which) -> {
                String username = usernameEdit.getText().toString().trim();
                String password = passwordEdit.getText().toString().trim();
                String fullName = fullNameEdit.getText().toString().trim();
                String roleStr = roleSpinner.getSelectedItem().toString();
                
                if (username.isEmpty() || password.isEmpty() || fullName.isEmpty()) {
                    Toast.makeText(this, "请填写完整信息", Toast.LENGTH_SHORT).show();
                    return;
                }
                
                User.UserRole role = roleStr.equals("管理员") ? User.UserRole.ADMIN : User.UserRole.EMPLOYEE;
                addUser(username, password, fullName, role);
            })
            .setNegativeButton("取消", null)
            .show();
    }
    
    private void addUser(String username, String password, String fullName, User.UserRole role) {
        executor.execute(() -> {
            try {
                // 检查用户名是否已存在
                User existingUser = database.userDao().getUserByUsername(username);
                if (existingUser != null) {
                    runOnUiThread(() -> {
                        Toast.makeText(this, "用户名已存在", Toast.LENGTH_SHORT).show();
                    });
                    return;
                }
                
                User newUser = new User(username, password, fullName, role);
                database.userDao().insertUser(newUser);
                
                runOnUiThread(() -> {
                    Toast.makeText(this, "用户添加成功", Toast.LENGTH_SHORT).show();
                    loadData();
                });
                
            } catch (Exception e) {
                runOnUiThread(() -> {
                    Toast.makeText(this, "添加用户失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                });
            }
        });
    }
    
    private void deleteUser(User user) {
        new android.app.AlertDialog.Builder(this)
            .setTitle("删除用户")
            .setMessage("确定要删除用户 " + user.getFullName() + " 吗？")
            .setPositiveButton("确定", (dialog, which) -> {
                executor.execute(() -> {
                    database.userDao().deactivateUser(user.getId());
                    runOnUiThread(() -> {
                        Toast.makeText(this, "用户已删除", Toast.LENGTH_SHORT).show();
                        loadData();
                    });
                });
            })
            .setNegativeButton("取消", null)
            .show();
    }
    
    private void upgradeCustomer(Customer customer) {
        new android.app.AlertDialog.Builder(this)
            .setTitle("升级客户等级")
            .setMessage("确定要升级客户 " + customer.getName() + " 的等级吗？")
            .setPositiveButton("确定", (dialog, which) -> {
                executor.execute(() -> {
                    customer.upgradeLevel();
                    database.customerDao().updateCustomer(customer);
                    runOnUiThread(() -> {
                        Toast.makeText(this, "客户等级已升级", Toast.LENGTH_SHORT).show();
                        loadData();
                    });
                });
            })
            .setNegativeButton("取消", null)
            .show();
    }
    
    // 用户适配器
    private class UserAdapter extends RecyclerView.Adapter<UserAdapter.UserViewHolder> {
        private List<User> users;
        
        public UserAdapter(List<User> users) {
            this.users = users;
        }
        
        @Override
        public UserViewHolder onCreateViewHolder(android.view.ViewGroup parent, int viewType) {
            LinearLayout itemLayout = new LinearLayout(UserManagementActivity.this);
            itemLayout.setOrientation(LinearLayout.HORIZONTAL);
            itemLayout.setBackgroundColor(0xFFF9F9F9);
            itemLayout.setPadding(16, 12, 16, 12);
            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
            layoutParams.setMargins(0, 0, 0, 4);
            itemLayout.setLayoutParams(layoutParams);
            
            return new UserViewHolder(itemLayout);
        }
        
        @Override
        public void onBindViewHolder(UserViewHolder holder, int position) {
            User user = users.get(position);
            holder.bind(user);
        }
        
        @Override
        public int getItemCount() {
            return users.size();
        }
        
        class UserViewHolder extends RecyclerView.ViewHolder {
            private TextView nameText, roleText, usernameText;
            private Button deleteButton;
            
            public UserViewHolder(View itemView) {
                super(itemView);
                
                LinearLayout layout = (LinearLayout) itemView;
                
                LinearLayout textLayout = new LinearLayout(UserManagementActivity.this);
                textLayout.setOrientation(LinearLayout.VERTICAL);
                LinearLayout.LayoutParams textParams = new LinearLayout.LayoutParams(
                    0, LinearLayout.LayoutParams.WRAP_CONTENT, 1.0f);
                textLayout.setLayoutParams(textParams);
                
                nameText = new TextView(UserManagementActivity.this);
                nameText.setTextSize(16);
                nameText.setTextColor(0xFF333333);
                nameText.setTypeface(null, android.graphics.Typeface.BOLD);
                textLayout.addView(nameText);
                
                usernameText = new TextView(UserManagementActivity.this);
                usernameText.setTextSize(14);
                usernameText.setTextColor(0xFF666666);
                textLayout.addView(usernameText);
                
                roleText = new TextView(UserManagementActivity.this);
                roleText.setTextSize(14);
                roleText.setTextColor(0xFFD81B60);
                textLayout.addView(roleText);
                
                layout.addView(textLayout);
                
                deleteButton = new Button(UserManagementActivity.this);
                deleteButton.setText("删除");
                deleteButton.setTextColor(0xFFFFFFFF);
                deleteButton.setBackgroundColor(0xFFFF5722);
                LinearLayout.LayoutParams deleteParams = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT, 80);
                deleteButton.setLayoutParams(deleteParams);
                layout.addView(deleteButton);
            }
            
            public void bind(User user) {
                nameText.setText(user.getFullName());
                usernameText.setText("用户名: " + user.getUsername());
                roleText.setText(user.getRole().getDisplayName());
                
                deleteButton.setOnClickListener(v -> deleteUser(user));
            }
        }
    }
    
    // 客户适配器
    private class CustomerAdapter extends RecyclerView.Adapter<CustomerAdapter.CustomerViewHolder> {
        private List<Customer> customers;
        
        public CustomerAdapter(List<Customer> customers) {
            this.customers = customers;
        }
        
        @Override
        public CustomerViewHolder onCreateViewHolder(android.view.ViewGroup parent, int viewType) {
            LinearLayout itemLayout = new LinearLayout(UserManagementActivity.this);
            itemLayout.setOrientation(LinearLayout.HORIZONTAL);
            itemLayout.setBackgroundColor(0xFFF9F9F9);
            itemLayout.setPadding(16, 12, 16, 12);
            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
            layoutParams.setMargins(0, 0, 0, 4);
            itemLayout.setLayoutParams(layoutParams);
            
            return new CustomerViewHolder(itemLayout);
        }
        
        @Override
        public void onBindViewHolder(CustomerViewHolder holder, int position) {
            Customer customer = customers.get(position);
            holder.bind(customer);
        }
        
        @Override
        public int getItemCount() {
            return customers.size();
        }
        
        class CustomerViewHolder extends RecyclerView.ViewHolder {
            private TextView nameText, phoneText, levelText, balanceText;
            private Button upgradeButton;
            
            public CustomerViewHolder(View itemView) {
                super(itemView);
                
                LinearLayout layout = (LinearLayout) itemView;
                
                LinearLayout textLayout = new LinearLayout(UserManagementActivity.this);
                textLayout.setOrientation(LinearLayout.VERTICAL);
                LinearLayout.LayoutParams textParams = new LinearLayout.LayoutParams(
                    0, LinearLayout.LayoutParams.WRAP_CONTENT, 1.0f);
                textLayout.setLayoutParams(textParams);
                
                nameText = new TextView(UserManagementActivity.this);
                nameText.setTextSize(16);
                nameText.setTextColor(0xFF333333);
                nameText.setTypeface(null, android.graphics.Typeface.BOLD);
                textLayout.addView(nameText);
                
                phoneText = new TextView(UserManagementActivity.this);
                phoneText.setTextSize(14);
                phoneText.setTextColor(0xFF666666);
                textLayout.addView(phoneText);
                
                levelText = new TextView(UserManagementActivity.this);
                levelText.setTextSize(14);
                levelText.setTextColor(0xFFD81B60);
                textLayout.addView(levelText);
                
                balanceText = new TextView(UserManagementActivity.this);
                balanceText.setTextSize(14);
                balanceText.setTextColor(0xFF4CAF50);
                textLayout.addView(balanceText);
                
                layout.addView(textLayout);
                
                upgradeButton = new Button(UserManagementActivity.this);
                upgradeButton.setText("升级");
                upgradeButton.setTextColor(0xFFFFFFFF);
                upgradeButton.setBackgroundColor(0xFF4CAF50);
                LinearLayout.LayoutParams upgradeParams = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT, 80);
                upgradeButton.setLayoutParams(upgradeParams);
                layout.addView(upgradeButton);
            }
            
            public void bind(Customer customer) {
                nameText.setText(customer.getName());
                phoneText.setText("电话: " + customer.getPhone());
                levelText.setText("等级: " + customer.getCustomerLevel());
                balanceText.setText("余额: ¥" + String.format("%.2f", customer.getWalletBalance()));
                
                upgradeButton.setOnClickListener(v -> upgradeCustomer(customer));
            }
        }
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
}