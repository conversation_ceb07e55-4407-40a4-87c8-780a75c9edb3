package com.example.cakeshop

import androidx.room.Room
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.cakeshop.database.CakeShopDatabase
import com.example.cakeshop.model.*
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.Assert.*

@RunWith(AndroidJUnit4::class)
class DatabaseTest {
    
    private lateinit var database: CakeShopDatabase
    
    @Before
    fun createDb() {
        database = Room.inMemoryDatabaseBuilder(
            ApplicationProvider.getApplicationContext(),
            CakeShopDatabase::class.java
        ).allowMainThreadQueries().build()
    }
    
    @After
    fun closeDb() {
        database.close()
    }
    
    @Test
    fun testProductOperations() = runBlocking {
        val productDao = database.productDao()
        
        // 测试添加产品
        val product = Product(
            name = "测试蛋糕",
            description = "这是一个测试蛋糕",
            price = 100.0,
            category = ProductCategory.BIRTHDAY_CAKE.name,
            size = CakeSize.MEDIUM.displayName,
            flavor = CakeFlavor.VANILLA.displayName,
            preparationTime = 2
        )
        
        val productId = productDao.insertProduct(product)
        assertTrue("产品ID应该大于0", productId > 0)
        
        // 测试查询产品
        val retrievedProduct = productDao.getProductById(productId)
        assertNotNull("应该能够查询到产品", retrievedProduct)
        assertEquals("产品名称应该匹配", "测试蛋糕", retrievedProduct?.name)
        
        // 测试获取所有产品
        val allProducts = productDao.getAllProducts().first()
        assertEquals("应该有1个产品", 1, allProducts.size)
        
        // 测试按类别查询
        val birthdayCakes = productDao.getProductsByCategory(ProductCategory.BIRTHDAY_CAKE.name).first()
        assertEquals("应该有1个生日蛋糕", 1, birthdayCakes.size)
        
        // 测试搜索功能
        val searchResults = productDao.searchProducts("测试").first()
        assertEquals("搜索应该返回1个结果", 1, searchResults.size)
    }
    
    @Test
    fun testCustomerOperations() = runBlocking {
        val customerDao = database.customerDao()
        
        // 测试添加客户
        val customer = Customer(
            name = "张三",
            phone = "13800138000",
            email = "<EMAIL>",
            customerType = CustomerType.VIP.name,
            totalOrders = 0,
            totalSpent = 0.0,
            loyaltyPoints = 0
        )
        
        val customerId = customerDao.insertCustomer(customer)
        assertTrue("客户ID应该大于0", customerId > 0)
        
        // 测试查询客户
        val retrievedCustomer = customerDao.getCustomerById(customerId)
        assertNotNull("应该能够查询到客户", retrievedCustomer)
        assertEquals("客户姓名应该匹配", "张三", retrievedCustomer?.name)
        
        // 测试按电话查询
        val customerByPhone = customerDao.getCustomerByPhone("13800138000")
        assertNotNull("应该能够通过电话查询到客户", customerByPhone)
        assertEquals("客户姓名应该匹配", "张三", customerByPhone?.name)
        
        // 测试更新客户统计
        customerDao.updateCustomerStats(customerId, 100.0, 10)
        val updatedCustomer = customerDao.getCustomerById(customerId)
        assertEquals("总消费应该更新", 100.0, updatedCustomer?.totalSpent ?: 0.0, 0.01)
        assertEquals("积分应该更新", 10, updatedCustomer?.loyaltyPoints ?: 0)
    }
    
    @Test
    fun testInventoryOperations() = runBlocking {
        val inventoryDao = database.inventoryDao()

        // 测试添加库存物料
        val inventoryItem = Inventory().apply {
            itemName = "面粉"
            category = Inventory.InventoryCategory.FLOUR.name
            unit = "kg"
            currentStock = 50.0
            minimumStock = 10.0
            unitCost = 8.0
            supplier = "面粉供应商"
        }

        val itemId = inventoryDao.insertInventory(inventoryItem)
        assertTrue("库存物料ID应该大于0", itemId > 0)

        // 测试查询库存
        val retrievedItem = inventoryDao.getInventoryById(itemId).first()
        assertNotNull("应该能够查询到库存物料", retrievedItem)
        assertEquals("物料名称应该匹配", "面粉", retrievedItem?.itemName)

        // 测试库存更新
        retrievedItem?.let { item ->
            item.currentStock = 30.0
            item.updatedAt = System.currentTimeMillis()
            inventoryDao.updateInventory(item)
        }
        val updatedItem = inventoryDao.getInventoryById(itemId).first()
        assertEquals("库存应该更新", 30.0, updatedItem?.currentStock ?: 0.0, 0.01)

        // 测试低库存查询
        updatedItem?.let { item ->
            item.currentStock = 5.0 // 设置为低于最低库存
            item.updatedAt = System.currentTimeMillis()
            inventoryDao.updateInventory(item)
        }
        val lowStockItems = inventoryDao.getLowStockItems().first()
        assertEquals("应该有1个低库存物料", 1, lowStockItems.size)
    }
    
    @Test
    fun testOrderOperations() = runBlocking {
        val customerDao = database.customerDao()
        val productDao = database.productDao()
        val orderDao = database.orderDao()
        val orderItemDao = database.orderItemDao()
        
        // 先创建客户和产品
        val customer = Customer(
            "李四",
            "13800138001",
            "<EMAIL>",
            Customer.CustomerType.REGULAR.name
        )
        val customerId = customerDao.insertCustomer(customer)
        
        val product = Product(
            "生日蛋糕",
            "美味的生日蛋糕",
            200.0,
            Product.ProductCategory.BIRTHDAY_CAKE.displayName,
            Product.CakeSize.MEDIUM.displayName,
            Product.CakeFlavor.CHOCOLATE.displayName,
            3
        )
        val productId = productDao.insertProductWithId(product)
        
        // 创建订单
        val order = Order(
            customerId,
            "ORD001",
            System.currentTimeMillis() + 24 * 60 * 60 * 1000 // 明天
        ).apply {
            status = Order.OrderStatus.PENDING.name
            totalAmount = 200.0
            finalAmount = 200.0
            paymentStatus = Order.PaymentStatus.UNPAID.name
        }
        val orderId = orderDao.insertOrder(order)
        
        // 创建订单项
        val orderItem = OrderItem(
            orderId,
            productId,
            1,
            200.0
        )
        orderItemDao.insertOrderItem(orderItem)
        
        // 测试查询订单
        val retrievedOrder = orderDao.getOrderByIdSync(orderId)
        assertNotNull("应该能够查询到订单", retrievedOrder)
        assertEquals("订单号应该匹配", "ORD001", retrievedOrder?.orderNumber)
        
        // 测试查询订单项
        val orderItems = orderItemDao.getOrderItemsByOrderIdSync(orderId)
        assertEquals("应该有1个订单项", 1, orderItems.size)
        assertEquals("订单项数量应该匹配", 1, orderItems[0].quantity)
        
        // 测试按客户查询订单
        val customerOrders = orderDao.getOrdersByCustomer(customerId).first()
        assertEquals("客户应该有1个订单", 1, customerOrders.size)
        
        // 测试按状态查询订单
        val pendingOrders = orderDao.getOrdersByStatus(OrderStatus.PENDING.name).first()
        assertEquals("应该有1个待处理订单", 1, pendingOrders.size)
    }
}
