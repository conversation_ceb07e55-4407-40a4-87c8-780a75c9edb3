# 🎂 蛋糕店管理系统 - 技术架构说明

## 🏗️ 系统架构概览

### 架构模式
本系统采用经典的 **MVC (Model-View-Controller)** 架构模式，确保代码结构清晰、职责分离、易于维护和扩展。

```
┌─────────────────────────────────────────────────────────┐
│                    View Layer (视图层)                    │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────┐ │
│  │  Activity/UI    │ │   Fragments     │ │   Dialogs   │ │
│  │   Components    │ │                 │ │             │ │
│  └─────────────────┘ └─────────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────┐
│                Controller Layer (控制层)                  │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────┐ │
│  │  SessionManager │ │   Repository    │ │  Utilities  │ │
│  │                 │ │                 │ │             │ │
│  └─────────────────┘ └─────────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────┐
│                   Model Layer (模型层)                    │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────┐ │
│  │   Data Models   │ │   DAO Interfaces│ │  Database   │ │
│  │   + Enums       │ │                 │ │   Room      │ │
│  └─────────────────┘ └─────────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────┘
```

## 📊 数据层设计

### 数据库架构
使用 **Room** 数据库框架，提供类型安全的SQLite数据库访问。

#### 核心表结构
```sql
-- 1. 产品表
CREATE TABLE products (
    product_id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    price REAL NOT NULL,
    category TEXT,
    size TEXT,
    flavor TEXT,
    preparation_time INTEGER,
    is_available BOOLEAN DEFAULT 1
);

-- 2. 客户表（扩展）
CREATE TABLE customers_java (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    phone TEXT NOT NULL,
    email TEXT,
    address TEXT,
    customer_type TEXT,
    wallet_balance REAL DEFAULT 0.0,    -- 新增：钱包余额
    customer_level TEXT DEFAULT 'BRONZE', -- 新增：客户等级
    total_orders INTEGER DEFAULT 0,
    total_spent REAL DEFAULT 0.0,
    loyalty_points INTEGER DEFAULT 0,
    created_at INTEGER,
    updated_at INTEGER
);

-- 3. 系统用户表（新增）
CREATE TABLE users_java (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    full_name TEXT NOT NULL,
    role TEXT NOT NULL,              -- ADMIN, EMPLOYEE
    is_active BOOLEAN DEFAULT 1,
    created_at INTEGER,
    last_login_at INTEGER
);

-- 4. 购物车表（新增）
CREATE TABLE cart_java (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL,
    unit_price REAL NOT NULL,
    customization TEXT,
    added_at INTEGER NOT NULL,
    FOREIGN KEY(customer_id) REFERENCES customers_java(id),
    FOREIGN KEY(product_id) REFERENCES products(product_id)
);

-- 5. 交易记录表（新增）
CREATE TABLE transactions_java (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER NOT NULL,
    transaction_type TEXT NOT NULL,  -- RECHARGE, PAYMENT, REFUND
    amount REAL NOT NULL,
    balance_before REAL NOT NULL,
    balance_after REAL NOT NULL,
    description TEXT,
    payment_method TEXT,
    order_id INTEGER DEFAULT 0,
    created_at INTEGER NOT NULL,
    FOREIGN KEY(customer_id) REFERENCES customers_java(id)
);
```

### 数据访问层 (DAO)
每个实体都有对应的DAO接口，提供标准的CRUD操作：

```java
@Dao
public interface CustomerDao {
    @Query("SELECT * FROM customers_java ORDER BY name ASC")
    LiveData<List<Customer>> getAllCustomers();

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insertCustomer(Customer customer);

    @Update
    void updateCustomer(Customer customer);

    @Delete
    void deleteCustomer(Customer customer);

    // 新增：钱包相关操作
    @Query("UPDATE customers_java SET walletBalance = :balance WHERE id = :id")
    void updateWalletBalance(long id, double balance);
}
```

### Room注解使用
为了避免Room编译警告，所有实体类都正确使用了注解：

```java
@Entity(tableName = "customers_java")
public class Customer {
    @PrimaryKey(autoGenerate = true)
    private long id;

    // 默认构造函数（Room使用）
    public Customer() {
        // 初始化默认值
    }

    // 便利构造函数（标记为@Ignore）
    @Ignore
    public Customer(String name, String phone, String email, String address) {
        this();
        this.name = name;
        this.phone = phone;
        this.email = email;
        this.address = address;
    }
}
```

## 🎮 控制层设计

### 会话管理 (SessionManager)
负责用户登录状态管理和权限控制：

```java
public class SessionManager {
    // 登录状态管理
    public void createLoginSession(User user);
    public boolean isLoggedIn();
    public void logoutUser();
    
    // 权限检查
    public boolean isAdmin();
    public boolean canAccessEmployeeManagement();
    public boolean canAccessReports();
}
```

### 数据仓储 (Repository)
统一数据访问接口，封装业务逻辑：

```java
public class CakeShopRepository {
    // 统计数据
    public int getTodayOrdersCountSync();
    public double getTodayRevenueSync();
    
    // 业务操作
    public void processOrder(Order order, List<OrderItem> items);
    public void processPayment(long customerId, double amount);
}
```

## 🎨 视图层设计

### Activity 架构
每个主要功能模块对应一个Activity：

```
MainActivity (主界面)
├── LoginActivity (登录)
├── RegisterActivity (注册) 🆕
├── ShoppingActivity (购物) 🆕
├── CartActivity (购物车) 🆕
├── CheckoutActivity (结算) 🆕
├── WalletActivity (钱包) 🆕
├── UserManagementActivity (用户管理) 🆕
├── ProductListActivity (产品管理)
├── OrderListActivity (订单管理)
├── CustomerListActivity (客户管理)
├── EmployeeListActivity (员工管理)
└── InventoryListActivity (库存管理)
```

### 界面设计原则
1. **Material Design 3**：遵循最新设计规范
2. **响应式布局**：适配不同屏幕尺寸
3. **用户友好**：直观的操作流程
4. **数据验证**：实时输入验证和错误提示

## 🔧 核心功能实现

### 1. 用户认证系统
```java
// 注册流程
public void performRegister() {
    // 1. 输入验证
    if (!validateInput()) return;
    
    // 2. 检查用户名唯一性
    User existingUser = database.userDao().getUserByUsername(username);
    if (existingUser != null) {
        showError("用户名已存在");
        return;
    }
    
    // 3. 创建用户账户
    User newUser = new User(username, password, fullName, UserRole.EMPLOYEE);
    long userId = database.userDao().insertUser(newUser);
    
    // 4. 创建客户档案
    Customer newCustomer = new Customer(fullName, phone, email, address);
    database.customerDao().insertCustomer(newCustomer);
}
```

### 2. 购物车系统
```java
// 添加商品到购物车
public void addToCart(Product product) {
    Cart existingItem = database.cartDao()
        .getCartItemByCustomerAndProduct(customerId, product.getProductId());
    
    if (existingItem != null) {
        // 增加数量
        existingItem.setQuantity(existingItem.getQuantity() + 1);
        database.cartDao().updateCartItem(existingItem);
    } else {
        // 添加新项目
        Cart newItem = new Cart(customerId, product.getProductId(), 1, product.getPrice());
        database.cartDao().insertCartItem(newItem);
    }
}
```

### 3. 支付系统
```java
// 钱包充值
public void performRecharge(double amount, String paymentMethod) {
    // 1. 获取当前余额
    double currentBalance = customer.getWalletBalance();
    
    // 2. 创建交易记录
    Transaction transaction = new Transaction();
    transaction.setCustomerId(customerId);
    transaction.setTransactionType(TransactionType.RECHARGE.name());
    transaction.setAmount(amount);
    transaction.setBalanceBefore(currentBalance);
    transaction.setBalanceAfter(currentBalance + amount);
    transaction.setPaymentMethod(paymentMethod);
    
    // 3. 更新余额
    customer.addToWallet(amount);
    database.customerDao().updateCustomer(customer);
    database.transactionDao().insertTransaction(transaction);
}
```

### 4. 订单管理系统
```java
// 订单状态管理
public void updateOrderStatus(Order order, String newStatus) {
    order.setStatus(newStatus);
    order.setUpdatedAt(System.currentTimeMillis());
    
    // 状态变更业务逻辑
    switch (newStatus) {
        case "CONFIRMED":
            // 确认订单后的处理
            break;
        case "SHIPPED":
            // 发货后的处理
            break;
        case "CANCELLED":
            // 取消订单后的处理（退款等）
            break;
    }
    
    repository.updateOrder(order);
}
```

## 🔐 安全性设计

### 权限控制
```java
// 基于角色的访问控制
public boolean canAccessUserManagement() {
    return getCurrentUserRole() == User.UserRole.ADMIN;
}

public boolean canAccessReports() {
    return getCurrentUserRole() == User.UserRole.ADMIN;
}
```

### 数据验证
```java
// 多层次数据验证
public boolean validateRegistration(String username, String password, String email) {
    // 1. 基础验证
    if (username.isEmpty() || password.isEmpty()) return false;
    
    // 2. 格式验证
    if (password.length() < 6) return false;
    
    // 3. 业务验证
    if (isUsernameExists(username)) return false;
    
    return true;
}
```

## 📈 性能优化

### 数据库优化
1. **索引策略**：为常用查询字段创建索引
2. **分页加载**：大数据量列表使用分页
3. **事务管理**：批量操作使用事务

### 内存管理
1. **ViewHolder模式**：RecyclerView使用ViewHolder
2. **资源释放**：及时释放数据库连接和线程
3. **图片优化**：使用适当的图片加载策略

### 响应性优化
1. **异步操作**：数据库操作在后台线程执行
2. **LiveData**：使用响应式数据更新UI
3. **加载状态**：提供清晰的加载和错误状态

## 🔄 扩展性设计

### 模块化架构
系统采用模块化设计，每个功能模块相对独立，便于：
1. **功能扩展**：新增功能模块
2. **维护更新**：独立模块维护
3. **团队协作**：多人并行开发

### 接口抽象
通过接口抽象，支持：
1. **数据源切换**：本地数据库 → 网络API
2. **支付方式扩展**：新增支付渠道
3. **通知系统**：集成推送服务

## 🎯 技术特色总结

### ✅ 架构优势
- **MVC模式**：清晰的分层架构
- **Room数据库**：类型安全的数据访问
- **响应式编程**：LiveData实时数据更新
- **权限控制**：基于角色的访问控制

### ✅ 功能完整
- **9个数据表**：支持复杂业务关系
- **完整CRUD**：所有模块支持增删改查
- **业务闭环**：从注册到支付的完整流程
- **管理功能**：全面的后台管理系统

### ✅ 用户体验
- **Material Design 3**：现代化界面设计
- **输入验证**：实时验证和友好提示
- **操作确认**：重要操作的安全确认
- **状态反馈**：清晰的操作状态提示

这个技术架构确保了系统的稳定性、可维护性和可扩展性，满足了课程设计的所有技术要求。
